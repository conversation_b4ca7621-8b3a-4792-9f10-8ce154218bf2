import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:async'; // للتعامل مع التأخير في البحث

import '../models/quran_model.dart';
import '../models/search_type.dart';
import '../services/quran_provider.dart';
import '../utils/logger.dart';

import '../widgets/islamic_background.dart';
import '../widgets/shimmer_loading.dart';
import '../widgets/enhanced_card.dart';
import '../widgets/enhanced_animations.dart';
import 'surah_detail_screen.dart';
import 'search_helpers.dart';

/// شاشة عرض القرآن الكريم
class QuranScreen extends StatefulWidget {
  const QuranScreen({super.key});

  @override
  State<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends State<QuranScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isInitialized = false;

  // متغيرات البحث
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce; // للتأخير في البحث
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // تهيئة مزود بيانات القرآن
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeQuranProvider();
    });
  }

  /// تهيئة مزود بيانات القرآن
  Future<void> _initializeQuranProvider() async {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    if (!_isInitialized) {
      await quranProvider.initialize();
      _isInitialized = true;
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context);
    final theme = Theme.of(context);

    // تحديد لون وأيقونة نوع البحث
    final bool isSurahSearch = quranProvider.searchType == SearchType.surahName;
    final Color searchTypeColor = isSurahSearch ? Colors.amber : Colors.green;

    return AppBar(
      title:
          quranProvider.isSearching
              ? null
              : const Text(
                'القرآن الكريم',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
      centerTitle: true,
      // إزالة زر العودة من شريط البحث
      leading: null,
      titleSpacing: quranProvider.isSearching ? 0 : null,
      actions: [
        // زر البحث
        if (!quranProvider.isSearching)
          Container(
            margin: const EdgeInsets.only(left: 8, right: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(30),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => _showSearchDialog(context),
              tooltip: 'بحث',
            ),
          ),
      ],
      // حقل البحث
      flexibleSpace:
          quranProvider.isSearching
              ? SafeArea(
                child: Container(
                  decoration: BoxDecoration(
                    color: theme.scaffoldBackgroundColor,
                    boxShadow: [
                      BoxShadow(
                        color: searchTypeColor.withAlpha(50),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: _buildSearchField(context),
                  ),
                ),
              )
              : null,
      elevation: 0,
      backgroundColor: theme.scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
     // Removed unused theme variable
    final quranProvider = Provider.of<QuranProvider>(context);

    // تحديد لون زر العودة حسب نوع البحث
    final bool isSurahSearch = quranProvider.searchType == SearchType.surahName;
    final Color searchTypeColor = isSurahSearch ? Colors.amber : Colors.green;

    return Scaffold(
      appBar: _buildAppBar(context),
      // إضافة زر عودة عائم عند البحث
      floatingActionButton:
          quranProvider.isSearching
              ? buildFloatingBackButton(context, searchTypeColor)
              : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
      body: IslamicBackground(
        opacity: 0.03,
        showPattern: true,
        showGradient: false,
        child: Column(
          children: [
            // محتوى رئيسي
            Expanded(
              child: Consumer<QuranProvider>(
                builder: (context, quranProvider, child) {
                  if (quranProvider.isLoading) {
                    return _buildLoadingState();
                  }

                  if (quranProvider.error.isNotEmpty) {
                    return _buildErrorState(quranProvider.error);
                  }

                  if (quranProvider.surahs.isEmpty) {
                    return _buildEmptyState();
                  }

                  // عرض نتائج البحث في الآيات إذا كان البحث عن آيات
                  if (quranProvider.isAyahSearch) {
                    AppLogger.info(
                      'عرض نتائج البحث في الآيات: ${quranProvider.searchResults.length} نتيجة',
                    );
                    return buildAyahSearchResults(
                      context,
                      quranProvider.searchResults,
                    );
                  }

                  return _buildSurahsList(quranProvider.surahs);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ShimmerLoading(
            isLoading: true,
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                final quranProvider = Provider.of<QuranProvider>(
                  context,
                  listen: false,
                );
                quranProvider.loadSurahs();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة القائمة الفارغة
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد سور متاحة',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                final quranProvider = Provider.of<QuranProvider>(
                  context,
                  listen: false,
                );
                quranProvider.loadSurahs();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة السور
  Widget _buildSurahsList(List<Surah> surahs) {
    final quranProvider = Provider.of<QuranProvider>(context);

    /// بناء بطاقة السورة في الصف
    Widget buildSurahRowCard(Surah surah) {
      final theme = Theme.of(context);

      // تحديد الألوان مسبقًا لتجنب إعادة حسابها
      final primaryColor = theme.colorScheme.primary;
      final primaryColorLight = primaryColor.withAlpha(25);
      final primaryColorMedium = primaryColor.withAlpha(75);

      // تحديد لون النوع مسبقًا
      final bool isMeccan = surah.revelationType == 'Meccan';
      final Color typeColor =
          isMeccan ? Colors.amber.withAlpha(50) : Colors.green.withAlpha(50);
      final Color typeTextColor =
          isMeccan ? Colors.amber.shade800 : Colors.green.shade800;

      // استخدام const للعناصر الثابتة
      const smallBorderRadius = BorderRadius.all(Radius.circular(6));

      // استخدام RepaintBoundary لتحسين الأداء
      return RepaintBoundary(
        child: EnhancedCard(
          margin: EdgeInsets.zero,
          onTap: () {
            HapticFeedback.lightImpact();
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SurahDetailScreen(surah: surah),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Row(
              children: [
                // رقم السورة
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: primaryColorLight,
                    shape: BoxShape.circle,
                    border: Border.all(color: primaryColorMedium, width: 1),
                  ),
                  child: Center(
                    child: Text(
                      '${surah.number}',
                      style: TextStyle(
                        color: primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),

                // معلومات السورة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // اسم السورة
                      Text(
                        surah.name,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      // عدد الآيات
                      Text(
                        '${surah.numberOfAyahs} آية',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),

                // نوع النزول
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: typeColor,
                    borderRadius: smallBorderRadius,
                  ),
                  child: Text(
                    isMeccan ? 'مكية' : 'مدنية',
                    style: TextStyle(fontSize: 9, color: typeTextColor),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // عرض رسالة عندما لا توجد نتائج بحث
    if (quranProvider.isSearching &&
        surahs.isEmpty &&
        !quranProvider.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withAlpha(128),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج للبحث',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'حاول البحث بكلمات أخرى',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // عرض مؤشر التحميل أثناء البحث في الآيات
    if (quranProvider.isSearching && quranProvider.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري البحث في الآيات...', textAlign: TextAlign.center),
          ],
        ),
      );
    }

    // عرض السور في وضع القائمة (الوضع الافتراضي الوحيد)
    // عرض السور في قائمة مع صفين في كل عنصر
    return ListView.builder(
      // تقليل الحشو لتحسين الأداء
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      // استخدام cacheExtent لتحميل المزيد من العناصر مسبقًا
      cacheExtent: 500,
      // تقليل physics للحصول على تمرير أكثر سلاسة
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
      itemCount: (surahs.length / 2).ceil(), // عدد الصفوف
      itemBuilder: (context, rowIndex) {
        // حساب مؤشرات السور في هذا الصف
        final firstIndex = rowIndex * 2;
        final secondIndex = firstIndex + 1;

        // التحقق من وجود السورة الثانية
        final hasSecondSurah = secondIndex < surahs.length;

        // استخدام RepaintBoundary لتحسين الأداء
        return RepaintBoundary(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                // السورة الأولى
                Expanded(
                  child: FadeInAnimation(
                    delay: Duration(milliseconds: rowIndex * 100),
                    child: buildSurahRowCard(surahs[firstIndex]),
                  ),
                ),

                const SizedBox(width: 8), // مسافة بين السورتين
                // السورة الثانية (إذا وجدت)
                Expanded(
                  child:
                      hasSecondSurah
                          ? FadeInAnimation(
                            delay: Duration(milliseconds: rowIndex * 100 + 50),
                            child: buildSurahRowCard(surahs[secondIndex]),
                          )
                          : const SizedBox(), // مساحة فارغة إذا لم توجد سورة ثانية
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء حقل البحث
  Widget _buildSearchField(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context);
    final theme = Theme.of(context);
    final TextEditingController searchController = _searchController;

    // تحديد لون وأيقونة نوع البحث
    final bool isSurahSearch = quranProvider.searchType == SearchType.surahName;
    final Color searchTypeColor = isSurahSearch ? Colors.amber : Colors.green;
    final IconData searchTypeIcon =
        isSurahSearch ? Icons.menu_book : Icons.format_quote;

    // إنشاء حقل البحث البسيط
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(color: searchTypeColor.withAlpha(100), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: searchTypeColor.withAlpha(40),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة نوع البحث
          Container(
            margin: const EdgeInsets.only(right: 8, left: 12),
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: searchTypeColor.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Icon(searchTypeIcon, size: 18, color: searchTypeColor),
          ),

          // حقل النص
          Expanded(
            child: TextField(
              controller: searchController,
              focusNode: _searchFocusNode,
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
              style: theme.textTheme.bodyMedium,
              decoration: InputDecoration(
                hintText: isSurahSearch ? 'ابحث عن سورة...' : 'ابحث عن آية...',
                hintTextDirection: TextDirection.rtl,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                isDense: true,
                suffixIcon:
                    quranProvider.searchQuery.isNotEmpty
                        ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: searchTypeColor,
                            size: 18,
                          ),
                          onPressed: () {
                            searchController.clear();
                            quranProvider.updateSearchQuery('');
                          },
                        )
                        : null,
              ),
              onChanged: (value) {
                // تنفيذ البحث فورًا أثناء الكتابة بدون تأخير
                quranProvider.updateSearchQuery(value);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار البحث
  void _showSearchDialog(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);

    // عرض مربع حوار لاختيار نوع البحث
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'اختر نوع البحث',
              textAlign: TextAlign.center,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // خيار البحث عن سورة
                _buildSearchOptionCard(
                  context: context,
                  title: 'البحث عن سورة',
                  subtitle: 'البحث في أسماء السور',
                  icon: Icons.menu_book,
                  color: Colors.amber,
                  onTap: () {
                    Navigator.pop(context);

                    // تأكد من أن حالة البحث مضبوطة بشكل صحيح
                    quranProvider.startSearch(SearchType.surahName);

                    // طلب التركيز على حقل البحث
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        _searchFocusNode.requestFocus();
                      }
                    });
                  },
                ),

                const SizedBox(height: 12),

                // خيار البحث عن آية
                _buildSearchOptionCard(
                  context: context,
                  title: 'البحث عن آية',
                  subtitle: 'البحث في نصوص الآيات',
                  icon: Icons.format_quote,
                  color: Colors.green,
                  onTap: () {
                    Navigator.pop(context);

                    // تأكد من أن حالة البحث مضبوطة بشكل صحيح
                    quranProvider.startSearch(SearchType.ayahText);
                    // تعيين البحث الشامل كافتراضي
                    quranProvider.setSearchInSpecificSurah(false);

                    // طلب التركيز على حقل البحث
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        _searchFocusNode.requestFocus();
                      }
                    });
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
            actionsPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
          ),
    );
  }

  /// بناء بطاقة خيار البحث
  Widget _buildSearchOptionCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isSelected = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? color : color.withAlpha(50),
              width: isSelected ? 2 : 1,
            ),
            color: isSelected ? color.withAlpha(20) : Colors.transparent,
          ),
          child: Row(
            children: [
              // أيقونة الخيار
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withAlpha(30),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    Icon(icon, color: color, size: 24),
                    if (isSelected)
                      Positioned(
                        right: -2,
                        top: -2,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 10,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 16),

              // نص الخيار
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected ? color : null,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color:
                            isSelected
                                ? color.withAlpha(200)
                                : Theme.of(
                                  context,
                                ).colorScheme.onSurface.withAlpha(180),
                      ),
                    ),
                  ],
                ),
              ),

              // سهم أو علامة اختيار
              isSelected
                  ? Icon(Icons.check_circle, size: 20, color: color)
                  : Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha(128),
                  ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر العودة العائم
  Widget buildFloatingBackButton(BuildContext context, Color color) {
    return FloatingActionButton(
      onPressed: () {
        final quranProvider = Provider.of<QuranProvider>(
          context,
          listen: false,
        );
        quranProvider.cancelSearch();
      },
      backgroundColor: color,
      child: const Icon(Icons.arrow_back, color: Colors.white),
    );
  }
}

