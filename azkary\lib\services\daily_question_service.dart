import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/daily_question.dart';

/// خدمة الأسئلة الإسلامية اليومية
class DailyQuestionService extends ChangeNotifier {
  static const String _questionsKey = 'daily_questions';
  static const String _answersKey = 'user_answers';
  static const String _statsKey = 'question_stats';
  static const String _lastQuestionDateKey = 'last_question_date';
  static const String _currentQuestionsKey = 'current_questions_ids';
  static const int _dailyQuestionsCount = 3;

  List<DailyQuestion> _questions = [];
  List<UserQuestionAnswer> _userAnswers = [];
  QuestionStats _stats = QuestionStats(
    totalQuestions: 0,
    correctAnswers: 0,
    wrongAnswers: 0,
    currentStreak: 0,
    longestStreak: 0,
    averageTime: 0.0,
    categoryStats: {},
  );

  List<DailyQuestion> _currentQuestions = [];
  bool _isLoading = false;
  int _currentQuestionIndex = 0;

  // Getters
  List<DailyQuestion> get questions => _questions;
  List<UserQuestionAnswer> get userAnswers => _userAnswers;
  QuestionStats get stats => _stats;
  List<DailyQuestion> get currentQuestions => _currentQuestions;
  DailyQuestion? get currentQuestion =>
      _currentQuestions.isNotEmpty &&
              _currentQuestionIndex < _currentQuestions.length
          ? _currentQuestions[_currentQuestionIndex]
          : null;
  bool get isLoading => _isLoading;
  bool get hasAnsweredToday => _getAnsweredQuestionsToday() > 0;
  int get currentQuestionIndex => _currentQuestionIndex;
  int get totalDailyQuestions => _dailyQuestionsCount;
  int get answeredQuestionsToday => _getAnsweredQuestionsToday();
  bool get hasCompletedAllToday =>
      _getAnsweredQuestionsToday() >= _dailyQuestionsCount;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _loadQuestions();
      await _loadUserAnswers();
      await _loadStats();
      await _setTodaysQuestion();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة الأسئلة اليومية: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  /// تحميل الأسئلة من التخزين المحلي أو إنشاء أسئلة افتراضية
  Future<void> _loadQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final questionsJson = prefs.getString(_questionsKey);

    if (questionsJson != null) {
      final List<dynamic> questionsList = json.decode(questionsJson);
      _questions = questionsList.map((q) => DailyQuestion.fromJson(q)).toList();
    } else {
      // إنشاء أسئلة افتراضية
      _questions = _createDefaultQuestions();
      await _saveQuestions();
    }
  }

  /// حفظ الأسئلة في التخزين المحلي
  Future<void> _saveQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final questionsJson = json.encode(
      _questions.map((q) => q.toJson()).toList(),
    );
    await prefs.setString(_questionsKey, questionsJson);
  }

  /// تحميل إجابات المستخدم
  Future<void> _loadUserAnswers() async {
    final prefs = await SharedPreferences.getInstance();
    final answersJson = prefs.getString(_answersKey);

    if (answersJson != null) {
      final List<dynamic> answersList = json.decode(answersJson);
      _userAnswers =
          answersList.map((a) => UserQuestionAnswer.fromJson(a)).toList();
    }
  }

  /// حفظ إجابات المستخدم
  Future<void> _saveUserAnswers() async {
    final prefs = await SharedPreferences.getInstance();
    final answersJson = json.encode(
      _userAnswers.map((a) => a.toJson()).toList(),
    );
    await prefs.setString(_answersKey, answersJson);
  }

  /// تحميل الإحصائيات
  Future<void> _loadStats() async {
    final prefs = await SharedPreferences.getInstance();
    final statsJson = prefs.getString(_statsKey);

    if (statsJson != null) {
      _stats = QuestionStats.fromJson(json.decode(statsJson));
    }
  }

  /// حفظ الإحصائيات
  Future<void> _saveStats() async {
    final prefs = await SharedPreferences.getInstance();
    final statsJson = json.encode(_stats.toJson());
    await prefs.setString(_statsKey, statsJson);
  }

  /// تحديد أسئلة اليوم (3 أسئلة)
  Future<void> _setTodaysQuestion() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';
    final lastDateString = prefs.getString(_lastQuestionDateKey);

    // التحقق من وجود أسئلة لليوم الحالي
    if (lastDateString == todayString) {
      // محاولة تحميل من التخزين المؤقت أولاً
      final cachedQuestions = await _loadCachedQuestions();
      if (cachedQuestions != null &&
          cachedQuestions.length >= _dailyQuestionsCount) {
        _currentQuestions = cachedQuestions;
        _currentQuestionIndex = _getAnsweredQuestionsToday();
        return;
      }

      // استرجاع أسئلة اليوم الحالي من المعرفات المحفوظة
      final currentQuestionsIds = prefs.getStringList(_currentQuestionsKey);
      if (currentQuestionsIds != null &&
          currentQuestionsIds.length >= _dailyQuestionsCount) {
        _currentQuestions = [];
        for (String idStr in currentQuestionsIds) {
          final id = int.parse(idStr);
          final question = _questions.firstWhere(
            (q) => q.id == id,
            orElse: () => _getRandomQuestion(),
          );
          _currentQuestions.add(question);
        }

        // التأكد من أن لدينا 3 أسئلة
        while (_currentQuestions.length < _dailyQuestionsCount) {
          _currentQuestions.add(_getRandomQuestion());
        }

        _currentQuestionIndex = _getAnsweredQuestionsToday();

        // حفظ في التخزين المؤقت
        await _cacheQuestionsForToday(_currentQuestions);
        return;
      }
    }

    // اختيار 3 أسئلة جديدة لليوم
    _currentQuestions = _getRandomQuestions(_dailyQuestionsCount);
    _currentQuestionIndex = 0;

    // التأكد من أن لدينا 3 أسئلة على الأقل
    while (_currentQuestions.length < _dailyQuestionsCount) {
      _currentQuestions.add(_getRandomQuestion());
    }

    if (_currentQuestions.length >= _dailyQuestionsCount) {
      final questionsIds =
          _currentQuestions.map((q) => q.id.toString()).toList();
      await prefs.setStringList(_currentQuestionsKey, questionsIds);
      await prefs.setString(_lastQuestionDateKey, todayString);

      // حفظ في التخزين المؤقت
      await _cacheQuestionsForToday(_currentQuestions);
    }
  }

  /// حفظ الأسئلة في التخزين المؤقت
  Future<void> _cacheQuestionsForToday(List<DailyQuestion> questions) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';

    // حفظ الأسئلة كـ JSON
    final questionsJson = questions.map((q) => q.toJson()).toList();
    await prefs.setString(
      'cached_questions_$todayString',
      json.encode(questionsJson),
    );

    // حذف الأسئلة القديمة
    await _cleanOldCachedQuestions();
  }

  /// حذف الأسئلة القديمة من التخزين المؤقت
  Future<void> _cleanOldCachedQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();
    final today = DateTime.now();

    for (String key in keys) {
      if (key.startsWith('cached_questions_')) {
        final dateString = key.replaceFirst('cached_questions_', '');
        try {
          final parts = dateString.split('-');
          final cachedDate = DateTime(
            int.parse(parts[0]),
            int.parse(parts[1]),
            int.parse(parts[2]),
          );

          // حذف الأسئلة الأقدم من يومين
          if (today.difference(cachedDate).inDays > 1) {
            await prefs.remove(key);
          }
        } catch (e) {
          // حذف المفاتيح التالفة
          await prefs.remove(key);
        }
      }
    }
  }

  /// تحميل الأسئلة من التخزين المؤقت
  Future<List<DailyQuestion>?> _loadCachedQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';

    final cachedJson = prefs.getString('cached_questions_$todayString');
    if (cachedJson != null) {
      try {
        final List<dynamic> questionsData = json.decode(cachedJson);
        return questionsData
            .map((data) => DailyQuestion.fromJson(data))
            .toList();
      } catch (e) {
        debugPrint('خطأ في تحميل الأسئلة المخزنة: $e');
      }
    }
    return null;
  }

  /// الحصول على الوقت المتبقي حتى منتصف الليل
  Duration getTimeUntilMidnight() {
    final now = DateTime.now();
    final midnight = DateTime(now.year, now.month, now.day + 1);
    return midnight.difference(now);
  }

  /// الحصول على عدد الأسئلة المجابة اليوم
  int _getAnsweredQuestionsToday() {
    final today = DateTime.now();
    return _userAnswers
        .where((answer) => _isSameDay(answer.answeredAt, today))
        .length;
  }

  /// الحصول على عدة أسئلة عشوائية لم يتم الإجابة عليها مؤخراً
  List<DailyQuestion> _getRandomQuestions(int count) {
    final recentAnswers =
        _userAnswers
            .where((a) => DateTime.now().difference(a.answeredAt).inDays < 30)
            .map((a) => a.questionId)
            .toSet();

    final availableQuestions =
        _questions.where((q) => !recentAnswers.contains(q.id)).toList();

    if (availableQuestions.length < count) {
      // إذا لم تكن هناك أسئلة كافية، أضف من جميع الأسئلة
      availableQuestions.addAll(_questions);
    }

    // خلط الأسئلة واختيار العدد المطلوب
    availableQuestions.shuffle(math.Random());
    return availableQuestions.take(count).toList();
  }

  /// الحصول على سؤال عشوائي لم يتم الإجابة عليه مؤخراً
  DailyQuestion _getRandomQuestion() {
    final randomQuestions = _getRandomQuestions(1);
    return randomQuestions.isNotEmpty
        ? randomQuestions.first
        : _questions[math.Random().nextInt(_questions.length)];
  }

  /// الإجابة على السؤال الحالي
  Future<bool> answerQuestion(
    int selectedAnswerIndex,
    int timeSpentSeconds,
  ) async {
    if (currentQuestion == null) {
      return false;
    }

    final question = currentQuestion!;
    final isCorrect = selectedAnswerIndex == question.correctAnswerIndex;

    // حفظ إجابة المستخدم
    final userAnswer = UserQuestionAnswer(
      questionId: question.id,
      selectedAnswerIndex: selectedAnswerIndex,
      isCorrect: isCorrect,
      answeredAt: DateTime.now(),
      timeSpentSeconds: timeSpentSeconds,
    );

    _userAnswers.add(userAnswer);
    await _saveUserAnswers();

    // تحديث الإحصائيات
    await _updateStats(userAnswer, question);

    // الانتقال للسؤال التالي
    _currentQuestionIndex++;

    notifyListeners();
    return isCorrect;
  }

  /// الحصول على السؤال التالي
  void nextQuestion() {
    if (_currentQuestionIndex < _currentQuestions.length - 1) {
      _currentQuestionIndex++;
      notifyListeners();
    }
  }

  /// الحصول على السؤال السابق
  void previousQuestion() {
    if (_currentQuestionIndex > 0) {
      _currentQuestionIndex--;
      notifyListeners();
    }
  }

  /// إعادة تعيين الحالة لليوم الجديد
  void resetForNewDay() {
    _currentQuestionIndex = 0;
    _currentQuestions.clear();
    notifyListeners();
  }

  /// تحديث الإحصائيات
  Future<void> _updateStats(
    UserQuestionAnswer answer,
    DailyQuestion question,
  ) async {
    final newTotalQuestions = _stats.totalQuestions + 1;
    final newCorrectAnswers =
        _stats.correctAnswers + (answer.isCorrect ? 1 : 0);
    final newWrongAnswers = _stats.wrongAnswers + (answer.isCorrect ? 0 : 1);

    // حساب المتوسط الجديد للوقت
    final totalTime =
        (_stats.averageTime * _stats.totalQuestions) + answer.timeSpentSeconds;
    final newAverageTime = totalTime / newTotalQuestions;

    // تحديث السلسلة الحالية
    int newCurrentStreak;
    if (answer.isCorrect) {
      newCurrentStreak = _stats.currentStreak + 1;
    } else {
      newCurrentStreak = 0;
    }

    // تحديث أطول سلسلة
    final newLongestStreak = math.max(_stats.longestStreak, newCurrentStreak);

    // تحديث إحصائيات الفئات
    final newCategoryStats = Map<String, int>.from(_stats.categoryStats);
    final category = question.category;
    newCategoryStats[category] = (newCategoryStats[category] ?? 0) + 1;

    _stats = QuestionStats(
      totalQuestions: newTotalQuestions,
      correctAnswers: newCorrectAnswers,
      wrongAnswers: newWrongAnswers,
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
      averageTime: newAverageTime,
      categoryStats: newCategoryStats,
    );

    await _saveStats();
  }

  /// التحقق من كون التاريخين في نفس اليوم
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// إنشاء أسئلة افتراضية
  List<DailyQuestion> _createDefaultQuestions() {
    return [
      DailyQuestion(
        id: 1,
        question: 'كم عدد سور القرآن الكريم؟',
        options: ['114', '113', '115', '112'],
        correctAnswerIndex: 0,
        explanation:
            'القرآن الكريم يحتوي على 114 سورة، من سورة الفاتحة إلى سورة الناس.',
        category: 'القرآن الكريم',
        source: 'القرآن الكريم',
        difficulty: 1,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 2,
        question: 'ما هي أطول سورة في القرآن الكريم؟',
        options: [
          'سورة البقرة',
          'سورة آل عمران',
          'سورة النساء',
          'سورة المائدة',
        ],
        correctAnswerIndex: 0,
        explanation:
            'سورة البقرة هي أطول سورة في القرآن الكريم وتحتوي على 286 آية.',
        category: 'القرآن الكريم',
        source: 'القرآن الكريم',
        difficulty: 1,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 3,
        question: 'كم عدد أركان الإسلام؟',
        options: ['5', '4', '6', '7'],
        correctAnswerIndex: 0,
        explanation:
            'أركان الإسلام خمسة: الشهادتان، والصلاة، والزكاة، والصوم، والحج.',
        category: 'العقيدة',
        source: 'الحديث الشريف',
        difficulty: 1,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 4,
        question: 'ما هي أقصر سورة في القرآن الكريم؟',
        options: ['سورة الكوثر', 'سورة الإخلاص', 'سورة الفلق', 'سورة الناس'],
        correctAnswerIndex: 0,
        explanation:
            'سورة الكوثر هي أقصر سورة في القرآن الكريم وتحتوي على 3 آيات فقط.',
        category: 'القرآن الكريم',
        source: 'القرآن الكريم',
        difficulty: 1,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 5,
        question: 'في أي عام هجري فُرضت الصلاة؟',
        options: [
          'في السنة الأولى',
          'في رحلة الإسراء والمعراج',
          'في السنة الثانية',
          'في السنة الثالثة',
        ],
        correctAnswerIndex: 1,
        explanation: 'فُرضت الصلاة في رحلة الإسراء والمعراج قبل الهجرة.',
        category: 'السيرة النبوية',
        source: 'السيرة النبوية',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 6,
        question: 'كم عدد أركان الوضوء؟',
        options: ['4', '5', '6', '7'],
        correctAnswerIndex: 0,
        explanation:
            'أركان الوضوء أربعة: غسل الوجه، وغسل اليدين إلى المرفقين، ومسح الرأس، وغسل الرجلين إلى الكعبين.',
        category: 'الفقه',
        source: 'الفقه الإسلامي',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 7,
        question: 'ما هو اسم ناقة النبي صلى الله عليه وسلم؟',
        options: ['القصواء', 'العضباء', 'الجدعاء', 'كلها صحيحة'],
        correctAnswerIndex: 3,
        explanation:
            'كان للنبي صلى الله عليه وسلم عدة نوق منها القصواء والعضباء والجدعاء.',
        category: 'السيرة النبوية',
        source: 'السيرة النبوية',
        difficulty: 3,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 8,
        question: 'كم مرة ذُكر اسم "محمد" في القرآن الكريم؟',
        options: ['4 مرات', '5 مرات', '6 مرات', '7 مرات'],
        correctAnswerIndex: 0,
        explanation: 'ذُكر اسم "محمد" في القرآن الكريم 4 مرات صراحة.',
        category: 'القرآن الكريم',
        source: 'القرآن الكريم',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 9,
        question: 'ما هي السورة التي تُسمى "قلب القرآن"؟',
        options: ['سورة يس', 'سورة الفاتحة', 'سورة البقرة', 'سورة الإخلاص'],
        correctAnswerIndex: 0,
        explanation: 'سورة يس تُسمى "قلب القرآن" كما ورد في الحديث الشريف.',
        category: 'القرآن الكريم',
        source: 'الحديث الشريف',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 10,
        question: 'كم عدد الصحابة الذين بُشروا بالجنة؟',
        options: ['10', '12', '8', '15'],
        correctAnswerIndex: 0,
        explanation:
            'العشرة المبشرون بالجنة هم: أبو بكر، عمر، عثمان، علي، طلحة، الزبير، عبد الرحمن بن عوف، سعد بن أبي وقاص، سعيد بن زيد، أبو عبيدة بن الجراح.',
        category: 'الصحابة',
        source: 'الحديث الشريف',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
    ];
  }
}
