import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/daily_question.dart';

/// خدمة الأسئلة الإسلامية اليومية
class DailyQuestionService extends ChangeNotifier {
  static const String _questionsKey = 'daily_questions';
  static const String _answersKey = 'user_answers';
  static const String _statsKey = 'question_stats';
  static const String _lastQuestionDateKey = 'last_question_date';
  static const String _currentQuestionIdKey = 'current_question_id';

  List<DailyQuestion> _questions = [];
  List<UserQuestionAnswer> _userAnswers = [];
  QuestionStats _stats = QuestionStats(
    totalQuestions: 0,
    correctAnswers: 0,
    wrongAnswers: 0,
    currentStreak: 0,
    longestStreak: 0,
    averageTime: 0.0,
    categoryStats: {},
  );

  DailyQuestion? _currentQuestion;
  DateTime? _lastQuestionDate;
  bool _isLoading = false;

  // Getters
  List<DailyQuestion> get questions => _questions;
  List<UserQuestionAnswer> get userAnswers => _userAnswers;
  QuestionStats get stats => _stats;
  DailyQuestion? get currentQuestion => _currentQuestion;
  bool get isLoading => _isLoading;
  bool get hasAnsweredToday =>
      _lastQuestionDate != null &&
      _isSameDay(_lastQuestionDate!, DateTime.now());

  /// تهيئة الخدمة
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _loadQuestions();
      await _loadUserAnswers();
      await _loadStats();
      await _loadLastQuestionDate();
      await _setTodaysQuestion();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة الأسئلة اليومية: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  /// تحميل الأسئلة من التخزين المحلي أو إنشاء أسئلة افتراضية
  Future<void> _loadQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final questionsJson = prefs.getString(_questionsKey);

    if (questionsJson != null) {
      final List<dynamic> questionsList = json.decode(questionsJson);
      _questions = questionsList.map((q) => DailyQuestion.fromJson(q)).toList();
    } else {
      // إنشاء أسئلة افتراضية
      _questions = _createDefaultQuestions();
      await _saveQuestions();
    }
  }

  /// حفظ الأسئلة في التخزين المحلي
  Future<void> _saveQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final questionsJson = json.encode(
      _questions.map((q) => q.toJson()).toList(),
    );
    await prefs.setString(_questionsKey, questionsJson);
  }

  /// تحميل إجابات المستخدم
  Future<void> _loadUserAnswers() async {
    final prefs = await SharedPreferences.getInstance();
    final answersJson = prefs.getString(_answersKey);

    if (answersJson != null) {
      final List<dynamic> answersList = json.decode(answersJson);
      _userAnswers =
          answersList.map((a) => UserQuestionAnswer.fromJson(a)).toList();
    }
  }

  /// حفظ إجابات المستخدم
  Future<void> _saveUserAnswers() async {
    final prefs = await SharedPreferences.getInstance();
    final answersJson = json.encode(
      _userAnswers.map((a) => a.toJson()).toList(),
    );
    await prefs.setString(_answersKey, answersJson);
  }

  /// تحميل الإحصائيات
  Future<void> _loadStats() async {
    final prefs = await SharedPreferences.getInstance();
    final statsJson = prefs.getString(_statsKey);

    if (statsJson != null) {
      _stats = QuestionStats.fromJson(json.decode(statsJson));
    }
  }

  /// حفظ الإحصائيات
  Future<void> _saveStats() async {
    final prefs = await SharedPreferences.getInstance();
    final statsJson = json.encode(_stats.toJson());
    await prefs.setString(_statsKey, statsJson);
  }

  /// تحميل تاريخ آخر سؤال
  Future<void> _loadLastQuestionDate() async {
    final prefs = await SharedPreferences.getInstance();
    final dateString = prefs.getString(_lastQuestionDateKey);
    if (dateString != null) {
      _lastQuestionDate = DateTime.parse(dateString);
    }
  }

  /// حفظ تاريخ آخر سؤال
  Future<void> _saveLastQuestionDate() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      _lastQuestionDateKey,
      DateTime.now().toIso8601String(),
    );
  }

  /// تحديد سؤال اليوم
  Future<void> _setTodaysQuestion() async {
    if (hasAnsweredToday) {
      // المستخدم أجاب على سؤال اليوم بالفعل
      final prefs = await SharedPreferences.getInstance();
      final currentQuestionId = prefs.getInt(_currentQuestionIdKey);
      if (currentQuestionId != null) {
        _currentQuestion = _questions.firstWhere(
          (q) => q.id == currentQuestionId,
          orElse: () => _getRandomQuestion(),
        );
      }
      return;
    }

    // اختيار سؤال جديد لليوم
    _currentQuestion = _getRandomQuestion();

    if (_currentQuestion != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_currentQuestionIdKey, _currentQuestion!.id);
    }
  }

  /// الحصول على سؤال عشوائي لم يتم الإجابة عليه مؤخراً
  DailyQuestion _getRandomQuestion() {
    final recentAnswers =
        _userAnswers
            .where((a) => DateTime.now().difference(a.answeredAt).inDays < 30)
            .map((a) => a.questionId)
            .toSet();

    final availableQuestions =
        _questions.where((q) => !recentAnswers.contains(q.id)).toList();

    if (availableQuestions.isEmpty) {
      // إذا تم الإجابة على جميع الأسئلة، اختر عشوائياً
      return _questions[math.Random().nextInt(_questions.length)];
    }

    return availableQuestions[math.Random().nextInt(availableQuestions.length)];
  }

  /// الإجابة على السؤال اليومي
  Future<bool> answerQuestion(
    int selectedAnswerIndex,
    int timeSpentSeconds,
  ) async {
    if (_currentQuestion == null || hasAnsweredToday) {
      return false;
    }

    final isCorrect =
        selectedAnswerIndex == _currentQuestion!.correctAnswerIndex;

    // حفظ إجابة المستخدم
    final userAnswer = UserQuestionAnswer(
      questionId: _currentQuestion!.id,
      selectedAnswerIndex: selectedAnswerIndex,
      isCorrect: isCorrect,
      answeredAt: DateTime.now(),
      timeSpentSeconds: timeSpentSeconds,
    );

    _userAnswers.add(userAnswer);
    await _saveUserAnswers();

    // تحديث الإحصائيات
    await _updateStats(userAnswer);

    // حفظ تاريخ الإجابة
    _lastQuestionDate = DateTime.now();
    await _saveLastQuestionDate();

    notifyListeners();
    return isCorrect;
  }

  /// تحديث الإحصائيات
  Future<void> _updateStats(UserQuestionAnswer answer) async {
    final newTotalQuestions = _stats.totalQuestions + 1;
    final newCorrectAnswers =
        _stats.correctAnswers + (answer.isCorrect ? 1 : 0);
    final newWrongAnswers = _stats.wrongAnswers + (answer.isCorrect ? 0 : 1);

    // حساب المتوسط الجديد للوقت
    final totalTime =
        (_stats.averageTime * _stats.totalQuestions) + answer.timeSpentSeconds;
    final newAverageTime = totalTime / newTotalQuestions;

    // تحديث السلسلة الحالية
    int newCurrentStreak;
    if (answer.isCorrect) {
      newCurrentStreak = _stats.currentStreak + 1;
    } else {
      newCurrentStreak = 0;
    }

    // تحديث أطول سلسلة
    final newLongestStreak = math.max(_stats.longestStreak, newCurrentStreak);

    // تحديث إحصائيات الفئات
    final newCategoryStats = Map<String, int>.from(_stats.categoryStats);
    final category = _currentQuestion!.category;
    newCategoryStats[category] = (newCategoryStats[category] ?? 0) + 1;

    _stats = QuestionStats(
      totalQuestions: newTotalQuestions,
      correctAnswers: newCorrectAnswers,
      wrongAnswers: newWrongAnswers,
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
      averageTime: newAverageTime,
      categoryStats: newCategoryStats,
    );

    await _saveStats();
  }

  /// التحقق من كون التاريخين في نفس اليوم
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// إنشاء أسئلة افتراضية
  List<DailyQuestion> _createDefaultQuestions() {
    return [
      DailyQuestion(
        id: 1,
        question: 'كم عدد سور القرآن الكريم؟',
        options: ['114', '113', '115', '112'],
        correctAnswerIndex: 0,
        explanation:
            'القرآن الكريم يحتوي على 114 سورة، من سورة الفاتحة إلى سورة الناس.',
        category: 'القرآن الكريم',
        source: 'القرآن الكريم',
        difficulty: 1,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 2,
        question: 'ما هي أطول سورة في القرآن الكريم؟',
        options: [
          'سورة البقرة',
          'سورة آل عمران',
          'سورة النساء',
          'سورة المائدة',
        ],
        correctAnswerIndex: 0,
        explanation:
            'سورة البقرة هي أطول سورة في القرآن الكريم وتحتوي على 286 آية.',
        category: 'القرآن الكريم',
        source: 'القرآن الكريم',
        difficulty: 1,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 3,
        question: 'كم عدد أركان الإسلام؟',
        options: ['5', '4', '6', '7'],
        correctAnswerIndex: 0,
        explanation:
            'أركان الإسلام خمسة: الشهادتان، والصلاة، والزكاة، والصوم، والحج.',
        category: 'العقيدة',
        source: 'الحديث الشريف',
        difficulty: 1,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 4,
        question: 'ما هي أقصر سورة في القرآن الكريم؟',
        options: ['سورة الكوثر', 'سورة الإخلاص', 'سورة الفلق', 'سورة الناس'],
        correctAnswerIndex: 0,
        explanation:
            'سورة الكوثر هي أقصر سورة في القرآن الكريم وتحتوي على 3 آيات فقط.',
        category: 'القرآن الكريم',
        source: 'القرآن الكريم',
        difficulty: 1,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 5,
        question: 'في أي عام هجري فُرضت الصلاة؟',
        options: [
          'في السنة الأولى',
          'في رحلة الإسراء والمعراج',
          'في السنة الثانية',
          'في السنة الثالثة',
        ],
        correctAnswerIndex: 1,
        explanation: 'فُرضت الصلاة في رحلة الإسراء والمعراج قبل الهجرة.',
        category: 'السيرة النبوية',
        source: 'السيرة النبوية',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 6,
        question: 'كم عدد أركان الوضوء؟',
        options: ['4', '5', '6', '7'],
        correctAnswerIndex: 0,
        explanation:
            'أركان الوضوء أربعة: غسل الوجه، وغسل اليدين إلى المرفقين، ومسح الرأس، وغسل الرجلين إلى الكعبين.',
        category: 'الفقه',
        source: 'الفقه الإسلامي',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 7,
        question: 'ما هو اسم ناقة النبي صلى الله عليه وسلم؟',
        options: ['القصواء', 'العضباء', 'الجدعاء', 'كلها صحيحة'],
        correctAnswerIndex: 3,
        explanation:
            'كان للنبي صلى الله عليه وسلم عدة نوق منها القصواء والعضباء والجدعاء.',
        category: 'السيرة النبوية',
        source: 'السيرة النبوية',
        difficulty: 3,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 8,
        question: 'كم مرة ذُكر اسم "محمد" في القرآن الكريم؟',
        options: ['4 مرات', '5 مرات', '6 مرات', '7 مرات'],
        correctAnswerIndex: 0,
        explanation: 'ذُكر اسم "محمد" في القرآن الكريم 4 مرات صراحة.',
        category: 'القرآن الكريم',
        source: 'القرآن الكريم',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 9,
        question: 'ما هي السورة التي تُسمى "قلب القرآن"؟',
        options: ['سورة يس', 'سورة الفاتحة', 'سورة البقرة', 'سورة الإخلاص'],
        correctAnswerIndex: 0,
        explanation: 'سورة يس تُسمى "قلب القرآن" كما ورد في الحديث الشريف.',
        category: 'القرآن الكريم',
        source: 'الحديث الشريف',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
      DailyQuestion(
        id: 10,
        question: 'كم عدد الصحابة الذين بُشروا بالجنة؟',
        options: ['10', '12', '8', '15'],
        correctAnswerIndex: 0,
        explanation:
            'العشرة المبشرون بالجنة هم: أبو بكر، عمر، عثمان، علي، طلحة، الزبير، عبد الرحمن بن عوف، سعد بن أبي وقاص، سعيد بن زيد، أبو عبيدة بن الجراح.',
        category: 'الصحابة',
        source: 'الحديث الشريف',
        difficulty: 2,
        dateAdded: DateTime.now(),
      ),
    ];
  }
}
