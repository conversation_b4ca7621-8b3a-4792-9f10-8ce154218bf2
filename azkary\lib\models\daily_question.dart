/// نموذج السؤال الإسلامي اليومي
class DailyQuestion {
  final int id;
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  final String explanation;
  final String category;
  final String source;
  final int difficulty; // 1: سهل، 2: متوسط، 3: صعب
  final DateTime dateAdded;

  DailyQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    required this.explanation,
    required this.category,
    required this.source,
    required this.difficulty,
    required this.dateAdded,
  });

  factory DailyQuestion.fromJson(Map<String, dynamic> json) {
    return DailyQuestion(
      id: json['id'] as int,
      question: json['question'] as String,
      options: List<String>.from(json['options'] as List),
      correctAnswerIndex: json['correctAnswerIndex'] as int,
      explanation: json['explanation'] as String,
      category: json['category'] as String,
      source: json['source'] as String,
      difficulty: json['difficulty'] as int,
      dateAdded: DateTime.parse(json['dateAdded'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'explanation': explanation,
      'category': category,
      'source': source,
      'difficulty': difficulty,
      'dateAdded': dateAdded.toIso8601String(),
    };
  }

  String get correctAnswer => options[correctAnswerIndex];

  String get difficultyText {
    switch (difficulty) {
      case 1:
        return 'سهل';
      case 2:
        return 'متوسط';
      case 3:
        return 'صعب';
      default:
        return 'غير محدد';
    }
  }
}

/// نموذج إجابة المستخدم على السؤال اليومي
class UserQuestionAnswer {
  final int questionId;
  final int selectedAnswerIndex;
  final bool isCorrect;
  final DateTime answeredAt;
  final int timeSpentSeconds;

  UserQuestionAnswer({
    required this.questionId,
    required this.selectedAnswerIndex,
    required this.isCorrect,
    required this.answeredAt,
    required this.timeSpentSeconds,
  });

  factory UserQuestionAnswer.fromJson(Map<String, dynamic> json) {
    return UserQuestionAnswer(
      questionId: json['questionId'] as int,
      selectedAnswerIndex: json['selectedAnswerIndex'] as int,
      isCorrect: json['isCorrect'] as bool,
      answeredAt: DateTime.parse(json['answeredAt'] as String),
      timeSpentSeconds: json['timeSpentSeconds'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'selectedAnswerIndex': selectedAnswerIndex,
      'isCorrect': isCorrect,
      'answeredAt': answeredAt.toIso8601String(),
      'timeSpentSeconds': timeSpentSeconds,
    };
  }
}

/// إحصائيات الأسئلة اليومية
class QuestionStats {
  final int totalQuestions;
  final int correctAnswers;
  final int wrongAnswers;
  final int currentStreak;
  final int longestStreak;
  final double averageTime;
  final Map<String, int> categoryStats;

  QuestionStats({
    required this.totalQuestions,
    required this.correctAnswers,
    required this.wrongAnswers,
    required this.currentStreak,
    required this.longestStreak,
    required this.averageTime,
    required this.categoryStats,
  });

  double get accuracy {
    if (totalQuestions == 0) return 0.0;
    return (correctAnswers / totalQuestions) * 100;
  }

  factory QuestionStats.fromJson(Map<String, dynamic> json) {
    return QuestionStats(
      totalQuestions: json['totalQuestions'] as int,
      correctAnswers: json['correctAnswers'] as int,
      wrongAnswers: json['wrongAnswers'] as int,
      currentStreak: json['currentStreak'] as int,
      longestStreak: json['longestStreak'] as int,
      averageTime: (json['averageTime'] as num).toDouble(),
      categoryStats: Map<String, int>.from(json['categoryStats'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalQuestions': totalQuestions,
      'correctAnswers': correctAnswers,
      'wrongAnswers': wrongAnswers,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'averageTime': averageTime,
      'categoryStats': categoryStats,
    };
  }
}

/// فئات الأسئلة الإسلامية
enum QuestionCategory {
  quran('القرآن الكريم'),
  hadith('الحديث الشريف'),
  fiqh('الفقه'),
  aqeedah('العقيدة'),
  seerah('السيرة النبوية'),
  islamic_history('التاريخ الإسلامي'),
  islamic_culture('الثقافة الإسلامية'),
  prophets('الأنبياء والرسل'),
  companions('الصحابة'),
  general('عام');

  const QuestionCategory(this.arabicName);
  final String arabicName;
}
