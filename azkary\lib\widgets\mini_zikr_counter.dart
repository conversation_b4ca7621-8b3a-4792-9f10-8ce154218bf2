import 'package:flutter/material.dart';
import '../services/azkar_settings_service.dart';

// استخدام StatefulWidget لتتبع حالة النقر مع تأثيرات محسنة
class MiniZikrCounter extends StatefulWidget {
  final int currentCount;
  final int totalCount;
  final Function() onIncrement;
  final Function()? onReset;

  const MiniZikrCounter({
    super.key,
    required this.currentCount,
    required this.totalCount,
    required this.onIncrement,
    this.onReset,
  });

  @override
  State<MiniZikrCounter> createState() => _MiniZikrCounterState();
}

class _MiniZikrCounterState extends State<MiniZikrCounter>
    with TickerProviderStateMixin {
  // متغير لتتبع حالة النقر
  bool _isPressed = false;

  // متحكمات الانيميشن المحسنة
  late AnimationController _scaleController;
  late AnimationController _rippleController;
  late AnimationController _bounceController;
  late AnimationController _glowController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rippleAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد متحكمات الانيميشن المحسنة
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 120),
      vsync: this,
    );

    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // إعداد الانيميشن المحسنة
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.92).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _rippleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rippleController, curve: Curves.easeOutCubic),
    );

    _bounceAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _bounceController, curve: Curves.elasticOut),
    );

    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rippleController.dispose();
    _bounceController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isCompleted = widget.currentCount >= widget.totalCount;

    // إذا اكتمل العد، نعرض زر التسبيح المكتمل مع العداد بداخله
    if (isCompleted) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // زر التسبيح المكتمل (عرض كامل)
          SizedBox(
            width: double.infinity, // جعل الزر بعرض كامل
            child: ElevatedButton(
              onPressed: null, // تعطيل الزر عند اكتمال العد
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    theme.colorScheme.primary, // استخدام لون السمة الأساسي
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 10,
                ),
                textStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                disabledBackgroundColor:
                    theme
                        .colorScheme
                        .primary, // استخدام لون السمة الأساسي عند تعطيل الزر
                disabledForegroundColor:
                    Colors.white, // لون النص عند تعطيل الزر
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center, // توسيط المحتوى
                children: [
                  // أيقونة التسبيح المكتمل
                  const Icon(Icons.check_circle, size: 16),
                  const SizedBox(width: 8),

                  // نص التسبيح المكتمل
                  const Text('تم التسبيح'),

                  // فاصل
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    height: 20,
                    width: 1,
                    color: Colors.white.withValues(
                      red: 255,
                      green: 255,
                      blue: 255,
                      alpha: 0.4,
                    ), // 100/255 ≈ 0.4
                  ),

                  // العداد المكتمل
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '${widget.currentCount}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '/${widget.totalCount}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(
                            red: 255,
                            green: 255,
                            blue: 255,
                            alpha: 0.78,
                          ), // 200/255 ≈ 0.78
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // رسالة "أحسنت" مع تأثير انتقالي
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: theme.colorScheme.primary, // استخدام لون السمة الأساسي
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  'أحسنت',
                  style: TextStyle(
                    color:
                        theme.colorScheme.primary, // استخدام لون السمة الأساسي
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // تم إزالة زر إعادة الضبط
        ],
      );
    }

    // إذا لم يكتمل العد، نعرض زر التسبيح العادي مع العداد بداخله (عرض كامل)
    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleController,
        _rippleController,
        _bounceController,
        _glowController,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * _bounceAnimation.value,
          child: Stack(
            children: [
              // تأثير التوهج الخارجي
              if (_glowAnimation.value > 0)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: theme.colorScheme.primary.withAlpha(
                            (100 * _glowAnimation.value).round(),
                          ),
                          blurRadius: 20 * _glowAnimation.value,
                          spreadRadius: 5 * _glowAnimation.value,
                        ),
                      ],
                    ),
                  ),
                ),

              // تأثير الموجة المحسن
              if (_rippleAnimation.value > 0)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.primary.withAlpha(
                          (150 * (1.0 - _rippleAnimation.value)).round(),
                        ),
                        width: 3 * (1.0 - _rippleAnimation.value),
                      ),
                    ),
                  ),
                ),

              // الزر الرئيسي
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () async {
                    // تأثير اهتزاز محسن من خدمة الإعدادات
                    await AzkarSettingsService.performVibration();

                    // تشغيل جميع التأثيرات المحسنة
                    _scaleController.forward().then((_) {
                      _scaleController.reverse();
                    });

                    _bounceController.forward().then((_) {
                      _bounceController.reverse();
                    });

                    _glowController.forward().then((_) {
                      Future.delayed(const Duration(milliseconds: 200), () {
                        if (mounted) _glowController.reverse();
                      });
                    });

                    _rippleController.forward().then((_) {
                      _rippleController.reset();
                    });

                    // تنفيذ زيادة العداد
                    widget.onIncrement();

                    // تنفيذ تأثير النقر المحسن
                    setState(() {
                      _isPressed = true;

                      // إعادة الزر إلى حالته الطبيعية بعد فترة قصيرة
                      Future.delayed(const Duration(milliseconds: 120), () {
                        if (mounted) {
                          setState(() {
                            _isPressed = false;
                          });
                        }
                      });
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _isPressed
                            ? theme.colorScheme.primary.withAlpha(200)
                            : theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 14,
                    ),
                    textStyle: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                    ),
                    elevation: _isPressed ? 2 : 6,
                    shadowColor: theme.colorScheme.primary.withAlpha(100),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // أيقونة التسبيح المحسنة
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(30),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(Icons.add_circle_outline, size: 18),
                      ),
                      const SizedBox(width: 12),

                      // نص التسبيح
                      const Text(
                        'سبح',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      // فاصل محسن
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 12),
                        height: 24,
                        width: 2,
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(60),
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),

                      // العداد المحسن
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(20),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // رقم العداد الحالي
                            Text(
                              '${widget.currentCount}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            // إجمالي العدد المطلوب
                            Text(
                              '/${widget.totalCount}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withAlpha(180),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
