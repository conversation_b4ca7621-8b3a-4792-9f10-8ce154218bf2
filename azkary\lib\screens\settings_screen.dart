import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/theme_mode_enum.dart';
import '../models/ayah_view_mode.dart';
import '../services/notification_service.dart';
import '../services/theme_provider.dart';
import '../services/quran_provider.dart';
import '../widgets/theme_color_picker.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_background.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final NotificationService _notificationService = NotificationService();

  bool _morningAzkarEnabled = false;
  TimeOfDay _morningAzkarTime = const TimeOfDay(hour: 7, minute: 0);

  bool _eveningAzkarEnabled = false;
  TimeOfDay _eveningAzkarTime = const TimeOfDay(hour: 17, minute: 0);

  bool _dailyZikrEnabled = false;
  TimeOfDay _dailyZikrTime = const TimeOfDay(hour: 12, minute: 0);

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _morningAzkarEnabled = prefs.getBool('morning_azkar_enabled') ?? false;
      _morningAzkarTime = TimeOfDay(
        hour: prefs.getInt('morning_azkar_hour') ?? 7,
        minute: prefs.getInt('morning_azkar_minute') ?? 0,
      );

      _eveningAzkarEnabled = prefs.getBool('evening_azkar_enabled') ?? false;
      _eveningAzkarTime = TimeOfDay(
        hour: prefs.getInt('evening_azkar_hour') ?? 17,
        minute: prefs.getInt('evening_azkar_minute') ?? 0,
      );

      _dailyZikrEnabled = prefs.getBool('daily_zikr_enabled') ?? false;
      _dailyZikrTime = TimeOfDay(
        hour: prefs.getInt('daily_zikr_hour') ?? 12,
        minute: prefs.getInt('daily_zikr_minute') ?? 0,
      );
    });
  }

  Future<void> _saveSettings() async {
    try {
      // إظهار مؤشر التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري حفظ الإعدادات...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      final prefs = await SharedPreferences.getInstance();

      // حفظ إعدادات أذكار الصباح
      await prefs.setBool('morning_azkar_enabled', _morningAzkarEnabled);
      await prefs.setInt('morning_azkar_hour', _morningAzkarTime.hour);
      await prefs.setInt('morning_azkar_minute', _morningAzkarTime.minute);

      // حفظ إعدادات أذكار المساء
      await prefs.setBool('evening_azkar_enabled', _eveningAzkarEnabled);
      await prefs.setInt('evening_azkar_hour', _eveningAzkarTime.hour);
      await prefs.setInt('evening_azkar_minute', _eveningAzkarTime.minute);

      // حفظ إعدادات ذكر اليوم
      await prefs.setBool('daily_zikr_enabled', _dailyZikrEnabled);
      await prefs.setInt('daily_zikr_hour', _dailyZikrTime.hour);
      await prefs.setInt('daily_zikr_minute', _dailyZikrTime.minute);

      // إلغاء جميع الإشعارات الحالية قبل إعادة جدولتها
      await _notificationService.cancelAllNotifications();

      // جدولة الإشعارات
      if (_morningAzkarEnabled) {
        await _notificationService.scheduleMorningAzkar();
      }

      if (_eveningAzkarEnabled) {
        await _notificationService.scheduleEveningAzkar();
      }

      if (_dailyZikrEnabled) {
        await _notificationService.scheduleDailyZikr();
      }

      // التحقق من أن الـ widget لا يزال موجودًا قبل إظهار رسالة النجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // إظهار رسالة خطأ في حالة فشل حفظ الإعدادات
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حفظ الإعدادات'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _selectTime(
    BuildContext context,
    TimeOfDay initialTime,
    Function(TimeOfDay) onTimeSelected,
  ) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              dayPeriodTextStyle: const TextStyle(fontFamily: 'Tajawal'),
              hourMinuteTextStyle: const TextStyle(fontFamily: 'Tajawal'),
              helpTextStyle: const TextStyle(fontFamily: 'Tajawal'),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedTime != null) {
      onTimeSelected(pickedTime);
    }
  }

  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final hours = timeOfDay.hour.toString().padLeft(2, '0');
    final minutes = timeOfDay.minute.toString().padLeft(2, '0');
    return '$hours:$minutes';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'الإعدادات',
        // إضافة زر الرجوع
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'رجوع',
        ),
      ),
      body: IslamicBackground(
        opacity: theme.brightness == Brightness.dark ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Theme settings
              Card(
                margin: const EdgeInsets.only(bottom: 16),
                color: theme.cardColor, // استخدام لون البطاقة من الثيم
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color:
                        theme.brightness == Brightness.dark
                            ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                            : Colors.grey.withAlpha(30),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'تخصيص المظهر',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Consumer<ThemeProvider>(
                        builder: (context, themeProvider, child) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'وضع المظهر',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),
                              // زر اختيار الوضع الفاتح
                              RadioListTile<AppThemeMode>(
                                title: const Text('وضع فاتح'),
                                value: AppThemeMode.light,
                                groupValue: themeProvider.themeMode,
                                onChanged: (value) {
                                  if (value != null) {
                                    themeProvider.setThemeMode(value);
                                  }
                                },
                              ),
                              // زر اختيار الوضع المعتم
                              RadioListTile<AppThemeMode>(
                                title: const Text('وضع معتم'),
                                subtitle: const Text('خلفية رمادية داكنة'),
                                value: AppThemeMode.dim,
                                groupValue: themeProvider.themeMode,
                                onChanged: (value) {
                                  if (value != null) {
                                    themeProvider.setThemeMode(value);
                                  }
                                },
                              ),
                              // زر اختيار الوضع الليلي
                              RadioListTile<AppThemeMode>(
                                title: const Text('وضع ليلي'),
                                subtitle: const Text('خلفية سوداء'),
                                value: AppThemeMode.dark,
                                groupValue: themeProvider.themeMode,
                                onChanged: (value) {
                                  if (value != null) {
                                    themeProvider.setThemeMode(value);
                                  }
                                },
                              ),
                            ],
                          );
                        },
                      ),
                      const SizedBox(height: 8),
                      const Divider(),
                      const SizedBox(height: 8),
                      // مكون اختيار الألوان
                      const ThemeColorPicker(),

                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 8),

                      // شريط تمرير لتغيير حجم الخط
                      Consumer<ThemeProvider>(
                        builder: (context, themeProvider, child) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'حجم الخط',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    '${(themeProvider.fontSize * 100).toInt()}%',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Text('أصغر'),
                                  Expanded(
                                    child: Slider(
                                      value: themeProvider.fontSize,
                                      min: 0.8,
                                      max: 1.5,
                                      divisions: 7,
                                      onChanged: (value) {
                                        themeProvider.setFontSize(value);
                                      },
                                    ),
                                  ),
                                  const Text('أكبر'),
                                ],
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),

              // Notifications settings
              Card(
                margin: const EdgeInsets.only(bottom: 16),
                color: theme.cardColor, // استخدام لون البطاقة من الثيم
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color:
                        theme.brightness == Brightness.dark
                            ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                            : Colors.grey.withAlpha(30),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'التنبيهات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Morning Azkar
                      SwitchListTile(
                        title: const Text('تنبيه أذكار الصباح'),
                        subtitle: Text(
                          'الوقت: ${_formatTimeOfDay(_morningAzkarTime)}',
                        ),
                        value: _morningAzkarEnabled,
                        onChanged: (value) {
                          setState(() {
                            _morningAzkarEnabled = value;
                          });
                        },
                      ),
                      if (_morningAzkarEnabled)
                        ListTile(
                          title: const Text('تعيين وقت أذكار الصباح'),
                          trailing: const Icon(Icons.access_time),
                          onTap: () async {
                            await _selectTime(context, _morningAzkarTime, (
                              time,
                            ) {
                              setState(() {
                                _morningAzkarTime = time;
                              });
                            });
                          },
                        ),

                      const Divider(),

                      // Evening Azkar
                      SwitchListTile(
                        title: const Text('تنبيه أذكار المساء'),
                        subtitle: Text(
                          'الوقت: ${_formatTimeOfDay(_eveningAzkarTime)}',
                        ),
                        value: _eveningAzkarEnabled,
                        onChanged: (value) {
                          setState(() {
                            _eveningAzkarEnabled = value;
                          });
                        },
                      ),
                      if (_eveningAzkarEnabled)
                        ListTile(
                          title: const Text('تعيين وقت أذكار المساء'),
                          trailing: const Icon(Icons.access_time),
                          onTap: () async {
                            await _selectTime(context, _eveningAzkarTime, (
                              time,
                            ) {
                              setState(() {
                                _eveningAzkarTime = time;
                              });
                            });
                          },
                        ),

                      const Divider(),

                      // Daily Zikr
                      SwitchListTile(
                        title: const Text('تنبيه ذكر اليوم'),
                        subtitle: Text(
                          'الوقت: ${_formatTimeOfDay(_dailyZikrTime)}',
                        ),
                        value: _dailyZikrEnabled,
                        onChanged: (value) {
                          setState(() {
                            _dailyZikrEnabled = value;
                          });
                        },
                      ),
                      if (_dailyZikrEnabled)
                        ListTile(
                          title: const Text('تعيين وقت ذكر اليوم'),
                          trailing: const Icon(Icons.access_time),
                          onTap: () async {
                            await _selectTime(context, _dailyZikrTime, (time) {
                              setState(() {
                                _dailyZikrTime = time;
                              });
                            });
                          },
                        ),
                    ],
                  ),
                ),
              ),

              // Quran settings
              Card(
                margin: const EdgeInsets.only(bottom: 16),
                color: theme.cardColor, // استخدام لون البطاقة من الثيم
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color:
                        theme.brightness == Brightness.dark
                            ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                            : Colors.grey.withAlpha(30),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'إعدادات القرآن الكريم',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // تم إزالة خيارات تغيير طريقة عرض قائمة السور

                      // طريقة عرض الآيات داخل السور
                      Consumer<ThemeProvider>(
                        builder: (context, themeProvider, child) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'طريقة عرض الآيات داخل السور',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),

                              // عرض كقائمة
                              RadioListTile<AyahViewMode>(
                                title: const Text('عرض كقائمة'),
                                subtitle: const Text('عرض الآيات كقائمة عادية'),
                                value: AyahViewMode.list,
                                groupValue: themeProvider.ayahViewMode,
                                onChanged: (value) {
                                  if (value != null) {
                                    themeProvider.setAyahViewMode(value);
                                  }
                                },
                              ),

                              // عرض كتفسير
                              RadioListTile<AyahViewMode>(
                                title: const Text('عرض كتفسير'),
                                subtitle: const Text(
                                  'عرض الآيات مع إمكانية إضافة التفسير',
                                ),
                                value: AyahViewMode.tafsir,
                                groupValue: themeProvider.ayahViewMode,
                                onChanged: (value) {
                                  if (value != null) {
                                    themeProvider.setAyahViewMode(value);
                                  }
                                },
                              ),

                              // عرض بتمرير متواصل
                              RadioListTile<AyahViewMode>(
                                title: const Text('تمرير متواصل'),
                                subtitle: const Text(
                                  'عرض الآيات بشكل متواصل مع علامات الأجزاء والأحزاب',
                                ),
                                value: AyahViewMode.continuousScroll,
                                groupValue: themeProvider.ayahViewMode,
                                onChanged: (value) {
                                  if (value != null) {
                                    themeProvider.setAyahViewMode(value);
                                  }
                                },
                              ),
                            ],
                          );
                        },
                      ),

                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 16),

                      // أزرار تحميل السور والتفسير
                      Consumer<QuranProvider>(
                        builder: (context, quranProvider, child) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'تحميل المحتوى للاستخدام بدون إنترنت',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 16),

                              // زر تحميل جميع السور
                              ElevatedButton.icon(
                                onPressed:
                                    quranProvider.isDownloadingSurahs
                                        ? null
                                        : () async {
                                          final result =
                                              await quranProvider
                                                  .downloadAllSurahs();
                                          if (result && context.mounted) {
                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(
                                              const SnackBar(
                                                content: Text(
                                                  'تم تحميل جميع السور بنجاح',
                                                ),
                                                duration: Duration(seconds: 2),
                                              ),
                                            );
                                          }
                                        },
                                icon:
                                    quranProvider.isDownloadingSurahs
                                        ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                          ),
                                        )
                                        : const Icon(Icons.download),
                                label: Text(
                                  quranProvider.isDownloadingSurahs
                                      ? 'جاري تحميل السور (${(quranProvider.downloadProgress * 100).toInt()}%)'
                                      : quranProvider.downloadedSurahs.length ==
                                          quranProvider.surahs.length
                                      ? 'تم تحميل جميع السور'
                                      : 'تحميل جميع السور',
                                ),
                                style: ElevatedButton.styleFrom(
                                  minimumSize: const Size(double.infinity, 48),
                                ),
                              ),

                              const SizedBox(height: 12),

                              // زر تحميل التفسير
                              ElevatedButton.icon(
                                onPressed:
                                    quranProvider.isDownloadingTafsir
                                        ? null
                                        : () async {
                                          final result =
                                              await quranProvider
                                                  .downloadAllTafsir();
                                          if (result && context.mounted) {
                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(
                                              const SnackBar(
                                                content: Text(
                                                  'تم تحميل التفسير بنجاح',
                                                ),
                                                duration: Duration(seconds: 2),
                                              ),
                                            );
                                          }
                                        },
                                icon:
                                    quranProvider.isDownloadingTafsir
                                        ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                          ),
                                        )
                                        : const Icon(Icons.download),
                                label: Text(
                                  quranProvider.isDownloadingTafsir
                                      ? 'جاري تحميل التفسير (${(quranProvider.downloadProgress * 100).toInt()}%)'
                                      : quranProvider
                                              .downloadedSurahs
                                              .isNotEmpty &&
                                          quranProvider
                                                  .downloadedSurahs
                                                  .length ==
                                              quranProvider.surahs.length
                                      ? 'تم تحميل التفسير'
                                      : 'تحميل التفسير',
                                ),
                                style: ElevatedButton.styleFrom(
                                  minimumSize: const Size(double.infinity, 48),
                                ),
                              ),

                              const SizedBox(height: 8),
                              const Text(
                                'ملاحظة: تحميل المحتوى يتيح لك استخدام التطبيق بدون إنترنت',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontStyle: FontStyle.italic,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),

              // About section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'عن التطبيق',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const ListTile(
                        title: Text('الإصدار'),
                        subtitle: Text('1.0.0'),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Save button
              ElevatedButton(
                onPressed: _saveSettings,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text(
                  'حفظ الإعدادات',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      ),
    )
  }
}
