import 'package:flutter/material.dart';
import 'package:adhan/adhan.dart';
import 'package:intl/intl.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';
import '../services/location_service.dart';
import '../services/offline_prayer_service.dart';

/// شاشة عرض أوقات الصلاة
class PrayerTimesScreen extends StatefulWidget {
  const PrayerTimesScreen({super.key});

  @override
  State<PrayerTimesScreen> createState() => _PrayerTimesScreenState();
}

class _PrayerTimesScreenState extends State<PrayerTimesScreen>
    with TickerProviderStateMixin {
  bool _isLoading = true;
  String _locationName = 'جاري تحديد الموقع...';
  Position? _currentPosition;
  PrayerTimes? _prayerTimes;
  String _errorMessage = '';

  // متغيرات للصلاة التالية
  String _nextPrayerName = '';
  DateTime? _nextPrayerTime;
  Duration _remainingTime = Duration.zero;
  late AnimationController _animationController;
  Timer? _timer;

  // متغيرات للعمل بدون إنترنت
  CalculationMethod _calculationMethod = CalculationMethod.egyptian;
  Madhab _madhab = Madhab.shafi;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _loadSavedSettings();
    _loadPrayerTimes();
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSavedSettings() async {
    try {
      final savedLocation = await OfflinePrayerService.getSavedLocation();
      final calculationMethod =
          await OfflinePrayerService.getSavedCalculationMethod();
      final madhab = await OfflinePrayerService.getSavedMadhab();

      setState(() {
        _calculationMethod = calculationMethod;
        _madhab = madhab;

        if (savedLocation != null) {
          _locationName = savedLocation['cityName'];
        }
      });
    } catch (e) {
      // تجاهل الأخطاء وتابع بالإعدادات الافتراضية
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل أوقات الصلاة
  Future<void> _loadPrayerTimes() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // أولاً: محاولة استخدام الموقع المحفوظ
      final savedLocation = await OfflinePrayerService.getSavedLocation();
      if (savedLocation != null) {
        final latitude = savedLocation['latitude'];
        final longitude = savedLocation['longitude'];
        final cityName = savedLocation['cityName'];

        _calculatePrayerTimesFromCoordinates(latitude, longitude, cityName);
        return;
      }

      // ثانياً: محاولة الحصول على الموقع الحالي
      final permissionStatus = await Permission.location.request();

      if (permissionStatus.isGranted) {
        final position = await LocationService.getCurrentPosition();

        if (position != null) {
          setState(() {
            _currentPosition = position;
            _locationName = 'موقعك الحالي';
          });

          // حفظ الموقع الحالي
          await OfflinePrayerService.saveLocation(
            position.latitude,
            position.longitude,
            'موقعك الحالي',
          );

          _calculatePrayerTimesFromCoordinates(
            position.latitude,
            position.longitude,
            'موقعك الحالي',
          );
        } else {
          _tryOfflineMode();
        }
      } else {
        _tryOfflineMode();
      }
    } catch (e) {
      _tryOfflineMode();
    }
  }

  /// محاولة استخدام الوضع المحلي
  void _tryOfflineMode() {
    setState(() {
      _errorMessage =
          'تعذر الحصول على الموقع. يرجى السماح بصلاحية الموقع أو التحقق من الاتصال بالإنترنت.';
      _isLoading = false;
    });
  }

  /// حساب أوقات الصلاة من الإحداثيات
  void _calculatePrayerTimesFromCoordinates(
    double latitude,
    double longitude,
    String cityName,
  ) {
    try {
      final prayerTimes = OfflinePrayerService.calculatePrayerTimes(
        latitude: latitude,
        longitude: longitude,
        method: _calculationMethod,
        madhab: _madhab,
      );

      if (prayerTimes != null) {
        setState(() {
          _prayerTimes = prayerTimes;
          _locationName = cityName;
          _isLoading = false;
          _errorMessage = '';
        });

        // تحديد الصلاة التالية وبدء المؤقت
        _updateNextPrayer();
        _startTimer();
      } else {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء حساب أوقات الصلاة';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء حساب أوقات الصلاة: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text('أوقات الصلاة'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPrayerTimes,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage.isNotEmpty
              ? _buildErrorWidget()
              : _buildPrayerTimesContent(isDarkMode),
    );
  }

  /// بناء محتوى أوقات الصلاة
  Widget _buildPrayerTimesContent(bool isDarkMode) {
    if (_prayerTimes == null) {
      return const Center(child: Text('لا توجد معلومات متاحة عن أوقات الصلاة'));
    }

    final theme = Theme.of(context);
    final today = DateTime.now();
    final dateFormat = DateFormat.yMMMMEEEEd('ar');

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // معلومات التاريخ والموقع البسيطة
          _buildSimpleDateLocation(theme, dateFormat, today),

          const SizedBox(height: 16),

          // عرض الصلاة التالية والوقت المتبقي
          _buildNextPrayerWidget(theme),

          const SizedBox(height: 20),

          // قائمة أوقات الصلاة المحسنة
          Expanded(child: _buildPrayerTimesGrid(theme, isDarkMode)),
        ],
      ),
    );
  }

  /// بناء معلومات التاريخ والموقع البسيطة
  Widget _buildSimpleDateLocation(
    ThemeData theme,
    DateFormat dateFormat,
    DateTime today,
  ) {
    return Column(
      children: [
        // التاريخ
        Text(
          dateFormat.format(today),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        // الموقع
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_on,
              color: theme.colorScheme.onSurface.withAlpha(150),
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              _locationName,
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.onSurface.withAlpha(150),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء شبكة أوقات الصلاة المحسنة والمتجاوبة
  Widget _buildPrayerTimesGrid(ThemeData theme, bool isDarkMode) {
    final prayers = [
      {
        'name': 'الفجر',
        'time': _prayerTimes!.fajr,
        'icon': Icons.brightness_3,
        'color': const Color(0xFF4A90E2),
      },
      {
        'name': 'الشروق',
        'time': _prayerTimes!.sunrise,
        'icon': Icons.wb_sunny_outlined,
        'color': const Color(0xFFFFB74D),
        'isNotPrayer': true,
      },
      {
        'name': 'الظهر',
        'time': _prayerTimes!.dhuhr,
        'icon': Icons.wb_sunny,
        'color': const Color(0xFFFF9800),
      },
      {
        'name': 'العصر',
        'time': _prayerTimes!.asr,
        'icon': Icons.wb_twighlight,
        'color': const Color(0xFFFF7043),
      },
      {
        'name': 'المغرب',
        'time': _prayerTimes!.maghrib,
        'icon': Icons.nights_stay_outlined,
        'color': const Color(0xFF7E57C2),
      },
      {
        'name': 'العشاء',
        'time': _prayerTimes!.isha,
        'icon': Icons.nights_stay,
        'color': const Color(0xFF5C6BC0),
      },
    ];

    // الحصول على حجم الشاشة لتحديد التخطيط المناسب
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isMediumScreen = screenWidth < 600;

    // تحديد عدد الأعمدة وحجم البطاقات حسب حجم الشاشة
    int crossAxisCount;
    double childAspectRatio;
    double spacing;

    if (isSmallScreen) {
      // للهواتف الصغيرة: عمود واحد مع بطاقات أفقية
      crossAxisCount = 1;
      childAspectRatio = 3.5;
      spacing = 8;
    } else if (isMediumScreen) {
      // للهواتف المتوسطة: عمودين مع نسبة محسنة
      crossAxisCount = 2;
      childAspectRatio = 1.6;
      spacing = 10;
    } else {
      // للشاشات الكبيرة: عمودين مع نسبة أوسع
      crossAxisCount = 2;
      childAspectRatio = 1.8;
      spacing = 12;
    }

    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
      ),
      itemCount: prayers.length,
      itemBuilder: (context, index) {
        final prayer = prayers[index];
        final isCurrentPrayer = _isCurrentPrayer(
          prayer['time'] as DateTime,
          index < prayers.length - 1
              ? prayers[index + 1]['time'] as DateTime
              : _getNextDayFajr(),
        );

        return _buildModernPrayerCard(
          prayer['name'] as String,
          prayer['time'] as DateTime,
          prayer['icon'] as IconData,
          prayer['color'] as Color,
          theme,
          isDarkMode,
          isCurrentPrayer: isCurrentPrayer,
          isNotPrayer: prayer['isNotPrayer'] as bool? ?? false,
          isSmallScreen: isSmallScreen,
        );
      },
    );
  }

  /// بناء بطاقة صلاة حديثة ومحسنة ومتجاوبة
  Widget _buildModernPrayerCard(
    String name,
    DateTime time,
    IconData icon,
    Color prayerColor,
    ThemeData theme,
    bool isDarkMode, {
    bool isCurrentPrayer = false,
    bool isNotPrayer = false,
    bool isSmallScreen = false,
  }) {
    final timeFormat = DateFormat.jm('ar');

    // تحديد الأحجام والمسافات حسب حجم الشاشة
    final double iconSize = isSmallScreen ? 20 : 24;
    final double fontSize = isSmallScreen ? 14 : 16;
    final double timeSize = isSmallScreen ? 12 : 14;
    final double padding = isSmallScreen ? 12 : 16;
    final double iconPadding = isSmallScreen ? 8 : 12;
    final double borderRadius = isSmallScreen ? 12 : 16;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors:
              isCurrentPrayer
                  ? [prayerColor.withAlpha(40), prayerColor.withAlpha(20)]
                  : isDarkMode
                  ? [prayerColor.withAlpha(15), prayerColor.withAlpha(8)]
                  : [prayerColor.withAlpha(10), prayerColor.withAlpha(5)],
        ),
        border: Border.all(
          color:
              isCurrentPrayer
                  ? prayerColor.withAlpha(100)
                  : prayerColor.withAlpha(30),
          width: isCurrentPrayer ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color:
                isCurrentPrayer
                    ? prayerColor.withAlpha(40)
                    : prayerColor.withAlpha(15),
            blurRadius: isCurrentPrayer ? 8 : 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(padding),
        child:
            isSmallScreen
                ? _buildHorizontalLayout(
                  name,
                  time,
                  icon,
                  prayerColor,
                  theme,
                  isDarkMode,
                  isCurrentPrayer,
                  isNotPrayer,
                  timeFormat,
                  iconSize,
                  fontSize,
                  timeSize,
                  iconPadding,
                )
                : _buildVerticalLayout(
                  name,
                  time,
                  icon,
                  prayerColor,
                  theme,
                  isDarkMode,
                  isCurrentPrayer,
                  isNotPrayer,
                  timeFormat,
                  iconSize,
                  fontSize,
                  timeSize,
                  iconPadding,
                ),
      ),
    );
  }

  /// تخطيط أفقي للشاشات الصغيرة
  Widget _buildHorizontalLayout(
    String name,
    DateTime time,
    IconData icon,
    Color prayerColor,
    ThemeData theme,
    bool isDarkMode,
    bool isCurrentPrayer,
    bool isNotPrayer,
    DateFormat timeFormat,
    double iconSize,
    double fontSize,
    double timeSize,
    double iconPadding,
  ) {
    return Row(
      children: [
        // أيقونة الصلاة
        Container(
          padding: EdgeInsets.all(iconPadding),
          decoration: BoxDecoration(
            color:
                isCurrentPrayer
                    ? prayerColor.withAlpha(80)
                    : prayerColor.withAlpha(30),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: prayerColor.withAlpha(20),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            icon,
            color:
                isCurrentPrayer
                    ? Colors.white
                    : isDarkMode
                    ? prayerColor.withAlpha(200)
                    : prayerColor,
            size: iconSize,
          ),
        ),
        const SizedBox(width: 12),
        // اسم الصلاة
        Expanded(
          child: Text(
            name,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: isCurrentPrayer ? FontWeight.bold : FontWeight.w600,
              color:
                  isCurrentPrayer ? prayerColor : theme.colorScheme.onSurface,
            ),
          ),
        ),
        // وقت الصلاة
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color:
                isCurrentPrayer
                    ? prayerColor.withAlpha(20)
                    : theme.colorScheme.surface.withAlpha(50),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color:
                  isCurrentPrayer
                      ? prayerColor.withAlpha(50)
                      : theme.colorScheme.outline.withAlpha(30),
              width: 1,
            ),
          ),
          child: Text(
            timeFormat.format(time),
            style: TextStyle(
              fontSize: timeSize,
              fontWeight: FontWeight.bold,
              color:
                  isCurrentPrayer
                      ? prayerColor
                      : theme.colorScheme.onSurface.withAlpha(180),
            ),
          ),
        ),
        // مؤشرات الحالة
        if (isCurrentPrayer || isNotPrayer) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color:
                  isCurrentPrayer
                      ? prayerColor
                      : theme.colorScheme.outline.withAlpha(30),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              isCurrentPrayer ? 'الآن' : 'وقت',
              style: TextStyle(
                fontSize: 8,
                fontWeight: FontWeight.bold,
                color:
                    isCurrentPrayer
                        ? Colors.white
                        : theme.colorScheme.onSurface.withAlpha(150),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// تخطيط عمودي للشاشات المتوسطة والكبيرة
  Widget _buildVerticalLayout(
    String name,
    DateTime time,
    IconData icon,
    Color prayerColor,
    ThemeData theme,
    bool isDarkMode,
    bool isCurrentPrayer,
    bool isNotPrayer,
    DateFormat timeFormat,
    double iconSize,
    double fontSize,
    double timeSize,
    double iconPadding,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // أيقونة الصلاة
        Container(
          padding: EdgeInsets.all(iconPadding),
          decoration: BoxDecoration(
            color:
                isCurrentPrayer
                    ? prayerColor.withAlpha(80)
                    : prayerColor.withAlpha(30),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: prayerColor.withAlpha(20),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            icon,
            color:
                isCurrentPrayer
                    ? Colors.white
                    : isDarkMode
                    ? prayerColor.withAlpha(200)
                    : prayerColor,
            size: iconSize,
          ),
        ),
        const SizedBox(height: 12),
        // اسم الصلاة
        Text(
          name,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: isCurrentPrayer ? FontWeight.bold : FontWeight.w600,
            color: isCurrentPrayer ? prayerColor : theme.colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        // وقت الصلاة
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color:
                isCurrentPrayer
                    ? prayerColor.withAlpha(20)
                    : theme.colorScheme.surface.withAlpha(50),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  isCurrentPrayer
                      ? prayerColor.withAlpha(50)
                      : theme.colorScheme.outline.withAlpha(30),
              width: 1,
            ),
          ),
          child: Text(
            timeFormat.format(time),
            style: TextStyle(
              fontSize: timeSize,
              fontWeight: FontWeight.bold,
              color:
                  isCurrentPrayer
                      ? prayerColor
                      : theme.colorScheme.onSurface.withAlpha(180),
            ),
          ),
        ),
        // مؤشر الصلاة الحالية
        if (isCurrentPrayer) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: prayerColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'الآن',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
        // مؤشر عدم كونها صلاة (مثل الشروق)
        if (isNotPrayer) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: theme.colorScheme.outline.withAlpha(30),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'وقت',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface.withAlpha(150),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadPrayerTimes,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// التحقق مما إذا كان الوقت الحالي هو وقت الصلاة المحدد
  bool _isCurrentPrayer(DateTime start, DateTime end) {
    final now = DateTime.now();
    return now.isAfter(start) && now.isBefore(end);
  }

  /// الحصول على وقت صلاة الفجر لليوم التالي
  DateTime _getNextDayFajr() {
    if (_prayerTimes == null || _currentPosition == null) {
      return DateTime.now().add(const Duration(days: 1));
    }

    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final tomorrowComponents = DateComponents(
      tomorrow.year,
      tomorrow.month,
      tomorrow.day,
    );
    final coordinates = Coordinates(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
    );
    final params = CalculationMethod.egyptian.getParameters();
    params.madhab = Madhab.shafi;

    final nextDayPrayers = PrayerTimes(coordinates, tomorrowComponents, params);
    return nextDayPrayers.fajr;
  }

  /// تحديث معلومات الصلاة التالية
  void _updateNextPrayer() {
    if (_prayerTimes == null) return;

    final now = DateTime.now();
    final prayers = [
      {'name': 'الفجر', 'time': _prayerTimes!.fajr},
      {'name': 'الشروق', 'time': _prayerTimes!.sunrise},
      {'name': 'الظهر', 'time': _prayerTimes!.dhuhr},
      {'name': 'العصر', 'time': _prayerTimes!.asr},
      {'name': 'المغرب', 'time': _prayerTimes!.maghrib},
      {'name': 'العشاء', 'time': _prayerTimes!.isha},
      {'name': 'الفجر (غدًا)', 'time': _getNextDayFajr()},
    ];

    // البحث عن الصلاة التالية
    for (int i = 0; i < prayers.length - 1; i++) {
      if (now.isBefore(prayers[i]['time'] as DateTime)) {
        setState(() {
          _nextPrayerName = prayers[i]['name'] as String;
          _nextPrayerTime = prayers[i]['time'] as DateTime;
          _calculateRemainingTime();
        });
        return;
      }
    }

    // إذا لم يتم العثور على صلاة تالية، فإن الصلاة التالية هي فجر الغد
    setState(() {
      _nextPrayerName = prayers.last['name'] as String;
      _nextPrayerTime = prayers.last['time'] as DateTime;
      _calculateRemainingTime();
    });
  }

  /// حساب الوقت المتبقي للصلاة التالية
  void _calculateRemainingTime() {
    if (_nextPrayerTime == null) return;

    final now = DateTime.now();
    setState(() {
      _remainingTime = _nextPrayerTime!.difference(now);
    });
  }

  /// بدء المؤقت لتحديث الوقت المتبقي
  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _calculateRemainingTime();

      // تحديث الصلاة التالية إذا انتهى الوقت
      if (_remainingTime.inSeconds <= 0) {
        _updateNextPrayer();
      }

      // تحريك الحركة
      if (_animationController.status == AnimationStatus.completed) {
        _animationController.reverse();
      } else if (_animationController.status == AnimationStatus.dismissed) {
        _animationController.forward();
      }
    });
  }

  /// تنسيق الوقت المتبقي بصيغة ساعات:دقائق:ثواني
  String _formatRemainingTime() {
    if (_remainingTime.inSeconds <= 0) {
      return 'حان وقت الصلاة';
    }

    final hours = _remainingTime.inHours;
    final minutes = _remainingTime.inMinutes.remainder(60);
    final seconds = _remainingTime.inSeconds.remainder(60);

    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// بناء مكون عرض الصلاة التالية المصغر
  Widget _buildNextPrayerWidget(ThemeData theme) {
    if (_nextPrayerTime == null) {
      return const SizedBox.shrink();
    }

    final timeFormat = DateFormat.jm('ar');
    final isDarkMode = theme.brightness == Brightness.dark;

    // أحجام مصغرة للويدجت
    final double padding = 12;
    final double borderRadius = 12;
    final double titleSize = 12;
    final double prayerNameSize = 14;
    final double timeSize = 16;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors:
              isDarkMode
                  ? [
                    theme.colorScheme.primary.withAlpha(40),
                    theme.colorScheme.secondary.withAlpha(30),
                    theme.colorScheme.tertiary.withAlpha(20),
                  ]
                  : [
                    theme.colorScheme.primary.withAlpha(20),
                    theme.colorScheme.secondary.withAlpha(15),
                    theme.colorScheme.tertiary.withAlpha(10),
                  ],
        ),
        border: Border.all(
          color: theme.colorScheme.primary.withAlpha(60),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withAlpha(30),
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          // عنوان الصلاة التالية مع أيقونة مصغرة
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.access_time,
                color: theme.colorScheme.primary,
                size: 16,
              ),
              SizedBox(width: 6),
              Text(
                'الصلاة القادمة',
                style: TextStyle(
                  fontSize: titleSize,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),

          // اسم الصلاة التالية مع أيقونة مصغرة
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getIconForPrayer(_nextPrayerName),
                color: theme.colorScheme.primary,
                size: 18,
              ),
              SizedBox(width: 8),
              Text(
                _nextPrayerName,
                style: TextStyle(
                  fontSize: prayerNameSize,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),

          // وقت الصلاة التالية مصغر
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.schedule,
                color: theme.colorScheme.secondary,
                size: 14,
              ),
              SizedBox(width: 4),
              Text(
                timeFormat.format(_nextPrayerTime!),
                style: TextStyle(
                  fontSize: timeSize,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.secondary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),

          // الوقت المتبقي مصغر
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.timer, color: theme.colorScheme.secondary, size: 14),
              SizedBox(width: 4),
              Text(
                _formatRemainingTime(),
                style: TextStyle(
                  fontSize: timeSize,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.secondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة مناسبة للصلاة
  IconData _getIconForPrayer(String prayerName) {
    switch (prayerName) {
      case 'الفجر':
      case 'الفجر (غدًا)':
        return Icons.brightness_3;
      case 'الشروق':
        return Icons.wb_sunny_outlined;
      case 'الظهر':
        return Icons.wb_sunny;
      case 'العصر':
        return Icons.wb_twighlight;
      case 'المغرب':
        return Icons.nights_stay_outlined;
      case 'العشاء':
        return Icons.nights_stay;
      default:
        return Icons.access_time;
    }
  }
}
