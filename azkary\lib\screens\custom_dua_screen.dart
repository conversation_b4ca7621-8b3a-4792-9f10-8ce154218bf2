import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_background.dart';

class CustomDuaScreen extends StatefulWidget {
  const CustomDuaScreen({super.key});

  @override
  State<CustomDuaScreen> createState() => _CustomDuaScreenState();
}

class _CustomDuaScreenState extends State<CustomDuaScreen> {
  final TextEditingController _feelingController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isLoading = false;
  String _duaResult = '';
  String _adviceResult = '';
  String _errorMessage = '';

  // Google Gemini AI API Key
  static const String _apiKey = 'AIzaSyBMUGgGueZZCt-lhS5J7IN3InuOTwb7dVs';

  @override
  void dispose() {
    _feelingController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _generateCustomDua() async {
    if (_feelingController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = 'يرجى كتابة مشاعرك أو حالتك النفسية';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _duaResult = '';
      _adviceResult = '';
    });

    try {
      // إنشاء نموذج Gemini
      final model = GenerativeModel(model: 'gemini-1.5-flash', apiKey: _apiKey);

      // إنشاء الـ prompt
      final prompt = '''
أنت مساعد إسلامي متخصص في الأدعية والنصائح الإسلامية. المستخدم يشعر بـ: "${_feelingController.text.trim()}"

يرجى تقديم:

1. **دعاء مخصص**: دعاء مناسب لحالته من القرآن الكريم والسنة النبوية الشريفة، مع ذكر المصدر إن أمكن.

2. **نصيحة إسلامية**: نصيحة قصيرة ومفيدة من الإسلام تناسب حالته النفسية.

تعليمات مهمة:
- الرد باللغة العربية فقط
- استخدم أدعية صحيحة من القرآن والسنة
- اجعل النصيحة مفيدة وعملية
- كن مهذباً ومتفهماً
- اجعل الرد مريحاً ومطمئناً للنفس

تنسيق الرد:
🤲 **الدعاء المخصص:**
[الدعاء هنا]

💡 **نصيحة إسلامية:**
[النصيحة هنا]
''';

      // إرسال الطلب
      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);

      if (response.text != null && response.text!.isNotEmpty) {
        _parseResponse(response.text!);
      } else {
        setState(() {
          _errorMessage = 'لم يتم الحصول على رد من الخدمة';
        });
      }
    } catch (e) {
      setState(() {
        if (e.toString().contains('API key')) {
          _errorMessage = 'خطأ في مفتاح API. يرجى التحقق من صحة المفتاح.';
        } else if (e.toString().contains('quota')) {
          _errorMessage = 'تم تجاوز حد الاستخدام اليومي. يرجى المحاولة لاحقاً.';
        } else if (e.toString().contains('suspended')) {
          _errorMessage = 'الخدمة معلقة مؤقتاً. يرجى المحاولة لاحقاً.';
        } else if (e.toString().contains('network')) {
          _errorMessage = 'خطأ في الشبكة. يرجى التحقق من الاتصال بالإنترنت.';
        } else {
          _errorMessage =
              'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.\nتفاصيل الخطأ: ${e.toString()}';
        }
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _parseResponse(String response) {
    try {
      // تقسيم الرد إلى دعاء ونصيحة
      final lines = response.split('\n');
      String dua = '';
      String advice = '';
      bool isDuaSection = false;
      bool isAdviceSection = false;

      for (String line in lines) {
        if (line.contains('الدعاء المخصص') || line.contains('🤲')) {
          isDuaSection = true;
          isAdviceSection = false;
          continue;
        } else if (line.contains('نصيحة إسلامية') || line.contains('💡')) {
          isDuaSection = false;
          isAdviceSection = true;
          continue;
        }

        if (isDuaSection && line.trim().isNotEmpty) {
          dua += '${line.trim()}\n';
        } else if (isAdviceSection && line.trim().isNotEmpty) {
          advice += '${line.trim()}\n';
        }
      }

      setState(() {
        _duaResult = dua.trim().isNotEmpty ? dua.trim() : response;
        _adviceResult = advice.trim();
      });

      // التمرير إلى النتائج
      Future.delayed(const Duration(milliseconds: 300), () {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      });
    } catch (e) {
      setState(() {
        _duaResult = response;
        _adviceResult = '';
      });
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ النص'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareText(String text) {
    share_plus.SharePlus.instance.share(share_plus.ShareParams(text: text));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: const CustomAppBar(title: 'دعاء مخصص'),
      body: IslamicBackground(
        opacity: isDarkMode ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // مقدمة
              Card(
                margin: const EdgeInsets.only(bottom: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.favorite,
                        size: 40,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'دعاء مخصص لحالتك',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'اكتب ما تشعر به أو حالتك النفسية الحالية، وسنقدم لك دعاء مناسب ونصيحة إسلامية مفيدة',
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              // حقل إدخال المشاعر
              Card(
                margin: const EdgeInsets.only(bottom: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'كيف تشعر الآن؟',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 12),
                      TextField(
                        controller: _feelingController,
                        maxLines: 4,
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.right,
                        decoration: InputDecoration(
                          hintText:
                              'مثال: أشعر بالقلق من المستقبل، أو أشعر بالحزن، أو أشعر بالامتنان...',
                          hintStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withAlpha(120),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.outline.withAlpha(50),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          filled: true,
                          fillColor: theme.colorScheme.surface.withAlpha(50),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // زر طلب الدعاء
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _generateCustomDua,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child:
                              _isLoading
                                  ? const Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                Colors.white,
                                              ),
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Text('جاري إنشاء الدعاء...'),
                                    ],
                                  )
                                  : const Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.auto_awesome, size: 20),
                                      SizedBox(width: 8),
                                      Text(
                                        'اطلب الدعاء',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // رسالة خطأ
              if (_errorMessage.isNotEmpty)
                Card(
                  margin: const EdgeInsets.only(bottom: 20),
                  color: Colors.red.withAlpha(20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(color: Colors.red.withAlpha(50)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        const Icon(Icons.error_outline, color: Colors.red),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _errorMessage,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // عرض الدعاء
              if (_duaResult.isNotEmpty)
                Card(
                  margin: const EdgeInsets.only(bottom: 20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.auto_awesome,
                              color: theme.colorScheme.primary,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'الدعاء المخصص',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withAlpha(10),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: theme.colorScheme.primary.withAlpha(30),
                            ),
                          ),
                          child: Text(
                            _duaResult,
                            style: TextStyle(
                              fontSize: 16,
                              height: 1.8,
                              color: theme.colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.right,
                            textDirection: TextDirection.rtl,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _copyToClipboard(_duaResult),
                                icon: const Icon(Icons.copy, size: 18),
                                label: const Text('نسخ'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: theme.colorScheme.primary,
                                  side: BorderSide(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _shareText(_duaResult),
                                icon: const Icon(Icons.share, size: 18),
                                label: const Text('مشاركة'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: theme.colorScheme.primary,
                                  side: BorderSide(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

              // عرض النصيحة
              if (_adviceResult.isNotEmpty)
                Card(
                  margin: const EdgeInsets.only(bottom: 20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: theme.colorScheme.secondary,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'نصيحة إسلامية',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.secondary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.secondary.withAlpha(10),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: theme.colorScheme.secondary.withAlpha(30),
                            ),
                          ),
                          child: Text(
                            _adviceResult,
                            style: TextStyle(
                              fontSize: 16,
                              height: 1.8,
                              color: theme.colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.right,
                            textDirection: TextDirection.rtl,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed:
                                    () => _copyToClipboard(_adviceResult),
                                icon: const Icon(Icons.copy, size: 18),
                                label: const Text('نسخ'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: theme.colorScheme.secondary,
                                  side: BorderSide(
                                    color: theme.colorScheme.secondary,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _shareText(_adviceResult),
                                icon: const Icon(Icons.share, size: 18),
                                label: const Text('مشاركة'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: theme.colorScheme.secondary,
                                  side: BorderSide(
                                    color: theme.colorScheme.secondary,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
