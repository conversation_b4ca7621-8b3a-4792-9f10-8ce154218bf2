import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_background.dart';

class CustomDuaScreen extends StatefulWidget {
  const CustomDuaScreen({super.key});

  @override
  State<CustomDuaScreen> createState() => _CustomDuaScreenState();
}

class _CustomDuaScreenState extends State<CustomDuaScreen> {
  final TextEditingController _feelingController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isLoading = false;
  String _duaResult = '';
  String _adviceResult = '';
  String _errorMessage = '';

  // Google Gemini AI API Key
  static const String _apiKey = 'AIzaSyD8pPmrujBBWunioC6jDYX5CAhVfaqwnpM';

  @override
  void dispose() {
    _feelingController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _generateCustomDua() async {
    if (_feelingController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = 'يرجى كتابة مشاعرك أو حالتك النفسية';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _duaResult = '';
      _adviceResult = '';
    });

    try {
      // إنشاء نموذج Gemini
      final model = GenerativeModel(model: 'gemini-1.5-flash', apiKey: _apiKey);

      // إنشاء الـ prompt
      final prompt = '''
أنت مساعد إسلامي متخصص في الأدعية القرآنية والنصائح الإسلامية. المستخدم يشعر بـ: "${_feelingController.text.trim()}"

يرجى تقديم دعاء مخصص ونصيحة إسلامية مناسبة لحالته النفسية.

المطلوب:
1. دعاء مخصص مرتبط بحالة المستخدم، مستوحى من القرآن الكريم أو دعاء شخصي مناسب
2. نصيحة إسلامية قصيرة ومفيدة

شروط الدعاء:
- يجب أن يكون الدعاء مرتبطاً مباشرة بما يشعر به المستخدم
- يفضل الاستعانة بآيات قرآنية أو أدعية من القرآن
- يمكن أن يكون دعاء مخصص بكلمات جميلة ومناسبة للحالة
- ليس بالضرورة أن يكون من الأحاديث النبوية
- اجعل الدعاء شخصياً ومؤثراً

شروط النصيحة:
- الرد باللغة العربية فقط
- اجعل النصيحة عملية ومطمئنة ومرتبطة بالقرآن
- كن مهذباً ومتفهماً لحالة المستخدم
- اذكر آية قرآنية مناسبة إن أمكن

تنسيق الرد بالضبط كما يلي:
🤲 **الدعاء المخصص:**
[اكتب دعاء مخصص مناسب لحالة المستخدم]

💡 **نصيحة إسلامية:**
[اكتب النصيحة هنا مع آية قرآنية إن أمكن]

مثال للتنسيق:
🤲 **الدعاء المخصص:**
اللهم اشرح لي صدري ويسر لي أمري، واجعل في قلبي نوراً وفي عقلي هدى، وارزقني السكينة والطمأنينة في كل أحوالي.

💡 **نصيحة إسلامية:**
تذكر قول الله تعالى: "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا" - الطلاق. ثق بأن الله معك ولن يتركك.
''';

      // إرسال الطلب
      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);

      if (response.text != null && response.text!.isNotEmpty) {
        _parseResponse(response.text!);
        // عرض النتائج في قائمة منبثقة
        _showDuaDialog(response.text!);
      } else {
        setState(() {
          _errorMessage = 'لم يتم الحصول على رد من الخدمة';
        });
      }
    } catch (e) {
      setState(() {
        if (e.toString().contains('API key')) {
          _errorMessage = 'خطأ في مفتاح API. يرجى التحقق من صحة المفتاح.';
        } else if (e.toString().contains('quota')) {
          _errorMessage = 'تم تجاوز حد الاستخدام اليومي. يرجى المحاولة لاحقاً.';
        } else if (e.toString().contains('suspended')) {
          _errorMessage = 'الخدمة معلقة مؤقتاً. يرجى المحاولة لاحقاً.';
        } else if (e.toString().contains('network')) {
          _errorMessage = 'خطأ في الشبكة. يرجى التحقق من الاتصال بالإنترنت.';
        } else {
          _errorMessage =
              'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.\nتفاصيل الخطأ: ${e.toString()}';
        }
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _parseResponse(String response) {
    try {
      // تنظيف النص وتقسيمه
      String cleanResponse = response.trim();

      // البحث عن أقسام الدعاء والنصيحة
      RegExp duaPattern = RegExp(
        r'🤲\s*\*\*الدعاء المخصص:\*\*(.*?)(?=💡|\$)',
        dotAll: true,
      );
      RegExp advicePattern = RegExp(
        r'💡\s*\*\*نصيحة إسلامية:\*\*(.*)',
        dotAll: true,
      );

      String dua = '';
      String advice = '';

      // استخراج الدعاء
      Match? duaMatch = duaPattern.firstMatch(cleanResponse);
      if (duaMatch != null) {
        dua = duaMatch.group(1)?.trim() ?? '';
      }

      // استخراج النصيحة
      Match? adviceMatch = advicePattern.firstMatch(cleanResponse);
      if (adviceMatch != null) {
        advice = adviceMatch.group(1)?.trim() ?? '';
      }

      // إذا لم يتم العثور على التنسيق المطلوب، جرب طريقة أخرى
      if (dua.isEmpty && advice.isEmpty) {
        final lines = cleanResponse.split('\n');
        bool isDuaSection = false;
        bool isAdviceSection = false;

        for (String line in lines) {
          String trimmedLine = line.trim();

          if (trimmedLine.contains('الدعاء المخصص') ||
              trimmedLine.contains('🤲')) {
            isDuaSection = true;
            isAdviceSection = false;
            continue;
          } else if (trimmedLine.contains('نصيحة إسلامية') ||
              trimmedLine.contains('💡')) {
            isDuaSection = false;
            isAdviceSection = true;
            continue;
          }

          if (isDuaSection &&
              trimmedLine.isNotEmpty &&
              !trimmedLine.startsWith('**')) {
            dua += '$trimmedLine\n';
          } else if (isAdviceSection &&
              trimmedLine.isNotEmpty &&
              !trimmedLine.startsWith('**')) {
            advice += '$trimmedLine\n';
          }
        }
      }

      setState(() {
        _duaResult = dua.trim();
        _adviceResult = advice.trim();
      });
    } catch (e) {
      setState(() {
        _duaResult = response;
        _adviceResult = '';
      });
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ النص'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareText(String text) {
    share_plus.SharePlus.instance.share(share_plus.ShareParams(text: text));
  }

  /// عرض النتائج في قائمة منبثقة جميلة
  void _showDuaDialog(String fullResponse) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: Colors.black.withAlpha(100),
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        final theme = Theme.of(context);

        return ScaleTransition(
          scale: Tween<double>(begin: 0.7, end: 1.0).animate(
            CurvedAnimation(parent: animation, curve: Curves.elasticOut),
          ),
          child: FadeTransition(
            opacity: animation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: const EdgeInsets.all(16),
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                  maxWidth: MediaQuery.of(context).size.width * 0.95,
                ),
                decoration: BoxDecoration(
                  color: theme.cardColor,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(50),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // شريط العنوان
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            theme.colorScheme.primary,
                            theme.colorScheme.primary.withAlpha(180),
                          ],
                        ),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.auto_awesome,
                            color: Colors.white,
                            size: 28,
                          ),
                          const SizedBox(width: 12),
                          const Expanded(
                            child: Text(
                              'دعاؤك المخصص',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.close, color: Colors.white),
                          ),
                        ],
                      ),
                    ),

                    // المحتوى
                    Flexible(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // عرض الدعاء
                            if (_duaResult.isNotEmpty) ...[
                              _buildDialogSection(
                                title: 'الدعاء المخصص',
                                content: _duaResult,
                                icon: Icons.auto_awesome,
                                color: theme.colorScheme.primary,
                                theme: theme,
                              ),
                              const SizedBox(height: 16),
                            ],

                            // عرض النصيحة
                            if (_adviceResult.isNotEmpty) ...[
                              _buildDialogSection(
                                title: 'نصيحة قرآنية',
                                content: _adviceResult,
                                icon: Icons.lightbulb_outline,
                                color: theme.colorScheme.secondary,
                                theme: theme,
                              ),
                              const SizedBox(height: 16),
                            ],

                            // إذا لم يتم تقسيم النص، اعرض النص كاملاً
                            if (_duaResult.isEmpty &&
                                _adviceResult.isEmpty) ...[
                              _buildDialogSection(
                                title: 'دعاؤك المخصص',
                                content: fullResponse,
                                icon: Icons.auto_awesome,
                                color: theme.colorScheme.primary,
                                theme: theme,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    // أزرار الإجراءات
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color:
                            theme.brightness == Brightness.dark
                                ? Colors.grey.withAlpha(20)
                                : Colors.grey.withAlpha(10),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(20),
                          bottomRight: Radius.circular(20),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () {
                                _copyToClipboard(fullResponse);
                                Navigator.of(context).pop();
                              },
                              icon: const Icon(Icons.copy, size: 18),
                              label: const Text('نسخ'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: theme.colorScheme.primary,
                                side: BorderSide(
                                  color: theme.colorScheme.primary,
                                ),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                _shareText(fullResponse);
                                Navigator.of(context).pop();
                              },
                              icon: const Icon(Icons.share, size: 18),
                              label: const Text('مشاركة'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء قسم في القائمة المنبثقة
  Widget _buildDialogSection({
    required String title,
    required String content,
    required IconData icon,
    required Color color,
    required ThemeData theme,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(10),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(30), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: TextStyle(
              fontSize: 15,
              height: 1.6,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.right,
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: const CustomAppBar(title: 'دعاء مخصص'),
      body: IslamicBackground(
        opacity: isDarkMode ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // مقدمة
              Card(
                margin: const EdgeInsets.only(bottom: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.favorite,
                        size: 40,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'دعاء مخصص لحالتك',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'اكتب ما تشعر به أو حالتك النفسية الحالية، وسنقدم لك دعاء مخصص مرتبط بحالتك ونصيحة قرآنية مفيدة',
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              // حقل إدخال المشاعر
              Card(
                margin: const EdgeInsets.only(bottom: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'كيف تشعر الآن؟',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 12),
                      TextField(
                        controller: _feelingController,
                        maxLines: 4,
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.right,
                        decoration: InputDecoration(
                          hintText:
                              'مثال: أشعر بالقلق من المستقبل، أو أحتاج للصبر، أو أريد الشكر والحمد...',
                          hintStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withAlpha(120),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.outline.withAlpha(50),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          filled: true,
                          fillColor: theme.colorScheme.surface.withAlpha(50),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // زر طلب الدعاء
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _generateCustomDua,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child:
                                _isLoading
                                    ? const Row(
                                      key: ValueKey('loading'),
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  Colors.white,
                                                ),
                                          ),
                                        ),
                                        SizedBox(width: 12),
                                        Text('جاري إنشاء الدعاء...'),
                                      ],
                                    )
                                    : const Row(
                                      key: ValueKey('button'),
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.auto_awesome, size: 20),
                                        SizedBox(width: 8),
                                        Text(
                                          'اطلب الدعاء',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // رسالة خطأ
              if (_errorMessage.isNotEmpty)
                Card(
                  margin: const EdgeInsets.only(bottom: 20),
                  color: Colors.red.withAlpha(20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(color: Colors.red.withAlpha(50)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        const Icon(Icons.error_outline, color: Colors.red),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _errorMessage,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // عرض الدعاء
              if (_duaResult.isNotEmpty)
                Card(
                  margin: const EdgeInsets.only(bottom: 20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.auto_awesome,
                              color: theme.colorScheme.primary,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'الدعاء المخصص',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withAlpha(10),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: theme.colorScheme.primary.withAlpha(30),
                            ),
                          ),
                          child: Text(
                            _duaResult,
                            style: TextStyle(
                              fontSize: 16,
                              height: 1.8,
                              color: theme.colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.right,
                            textDirection: TextDirection.rtl,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _copyToClipboard(_duaResult),
                                icon: const Icon(Icons.copy, size: 18),
                                label: const Text('نسخ'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: theme.colorScheme.primary,
                                  side: BorderSide(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _shareText(_duaResult),
                                icon: const Icon(Icons.share, size: 18),
                                label: const Text('مشاركة'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: theme.colorScheme.primary,
                                  side: BorderSide(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

              // عرض النصيحة
              if (_adviceResult.isNotEmpty)
                Card(
                  margin: const EdgeInsets.only(bottom: 20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: theme.colorScheme.secondary,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'نصيحة قرآنية',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.secondary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.secondary.withAlpha(10),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: theme.colorScheme.secondary.withAlpha(30),
                            ),
                          ),
                          child: Text(
                            _adviceResult,
                            style: TextStyle(
                              fontSize: 16,
                              height: 1.8,
                              color: theme.colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.right,
                            textDirection: TextDirection.rtl,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed:
                                    () => _copyToClipboard(_adviceResult),
                                icon: const Icon(Icons.copy, size: 18),
                                label: const Text('نسخ'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: theme.colorScheme.secondary,
                                  side: BorderSide(
                                    color: theme.colorScheme.secondary,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _shareText(_adviceResult),
                                icon: const Icon(Icons.share, size: 18),
                                label: const Text('مشاركة'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: theme.colorScheme.secondary,
                                  side: BorderSide(
                                    color: theme.colorScheme.secondary,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
