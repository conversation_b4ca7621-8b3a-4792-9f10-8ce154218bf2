import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../services/daily_question_service.dart';
import '../models/daily_question.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_background.dart';
import '../utils/page_transitions.dart';
import '../utils/color_extensions.dart';

class DailyQuestionScreen extends StatefulWidget {
  const DailyQuestionScreen({super.key});

  @override
  State<DailyQuestionScreen> createState() => _DailyQuestionScreenState();
}

class _DailyQuestionScreenState extends State<DailyQuestionScreen>
    with TickerProviderStateMixin {
  int? _selectedAnswerIndex;
  bool _showResult = false;
  bool _isAnswered = false;
  Timer? _timer;
  int _timeSpent = 0;
  late AnimationController _pulseController;
  late AnimationController _resultController;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);

    _resultController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pulseController.dispose();
    _resultController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isAnswered) {
        setState(() {
          _timeSpent++;
        });
      }
    });
  }

  void _selectAnswer(int index) {
    if (_isAnswered) return;

    setState(() {
      _selectedAnswerIndex = index;
    });
  }

  Future<void> _submitAnswer() async {
    if (_selectedAnswerIndex == null || _isAnswered) return;

    setState(() {
      _isAnswered = true;
    });

    final questionService = context.read<DailyQuestionService>();
    final isCorrect = await questionService.answerQuestion(
      _selectedAnswerIndex!,
      _timeSpent,
    );

    setState(() {
      _showResult = true;
    });

    _resultController.forward();

    // إظهار النتيجة مع تأثير صوتي (اختياري)
    if (isCorrect) {
      _showSuccessDialog();
    } else {
      _showFailureDialog();
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AnimatedWidgets.fadeInCard(
            index: 0,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.greenWithOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 60,
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'أحسنت! 🎉',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  const Text('إجابة صحيحة', style: TextStyle(fontSize: 16)),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('متابعة'),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _showFailureDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AnimatedWidgets.fadeInCard(
            index: 0,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.orangeWithOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.lightbulb,
                      color: Colors.orange,
                      size: 60,
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'لا بأس! 💪',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'تعلمنا شيئاً جديداً',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('متابعة'),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'السؤال اليومي',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.primaryWithOpacity(
                theme.colorScheme.primary,
                0.1,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.timer, size: 16, color: theme.colorScheme.primary),
                const SizedBox(width: 4),
                Text(
                  _formatTime(_timeSpent),
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: IslamicBackground(
        opacity: theme.brightness == Brightness.dark ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: Consumer<DailyQuestionService>(
          builder: (context, questionService, child) {
            if (questionService.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (questionService.hasAnsweredToday && !_showResult) {
              return _buildAlreadyAnsweredView(questionService);
            }

            final question = questionService.currentQuestion;
            if (question == null) {
              return _buildNoQuestionView();
            }

            return _buildQuestionView(question, theme);
          },
        ),
      ),
    );
  }

  Widget _buildQuestionView(DailyQuestion question, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // عنوان السؤال
          AnimatedWidgets.fadeInCard(
            index: 0,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color:
                      theme.brightness == Brightness.dark
                          ? AppColors.greyWithOpacity(0.3)
                          : AppColors.greyWithOpacity(0.2),
                ),
                boxShadow: [
                  BoxShadow(
                    color:
                        theme.brightness == Brightness.dark
                            ? AppColors.blackWithOpacity(0.3)
                            : AppColors.blackWithOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primaryWithOpacity(
                            theme.colorScheme.primary,
                            0.1,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          question.category,
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getDifficultyColor(
                            question.difficulty,
                          ).withOpacityValue(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          question.difficultyText,
                          style: TextStyle(
                            color: _getDifficultyColor(question.difficulty),
                            fontWeight: FontWeight.bold,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Text(
                    question.question,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // خيارات الإجابة
          ...question.options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final isSelected = _selectedAnswerIndex == index;
            final isCorrect = index == question.correctAnswerIndex;
            final isWrong = _showResult && isSelected && !isCorrect;

            return AnimatedWidgets.fadeInCard(
              index: index + 1,
              child: Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => _selectAnswer(index),
                    borderRadius: BorderRadius.circular(12),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: _getOptionColor(
                          isSelected,
                          isCorrect,
                          isWrong,
                          theme,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _getOptionBorderColor(
                            isSelected,
                            isCorrect,
                            isWrong,
                            theme,
                          ),
                          width: 2,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _getOptionIconColor(
                                isSelected,
                                isCorrect,
                                isWrong,
                                theme,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                String.fromCharCode(65 + index), // A, B, C, D
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              option,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight:
                                    isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                          ),
                          if (_showResult && isCorrect)
                            const Icon(Icons.check_circle, color: Colors.green),
                          if (isWrong)
                            const Icon(Icons.cancel, color: Colors.red),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          }),

          const SizedBox(height: 20),

          // زر الإرسال
          if (!_showResult)
            AnimatedWidgets.fadeInCard(
              index: question.options.length + 1,
              child: AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return Transform.scale(
                    scale:
                        _selectedAnswerIndex != null
                            ? 1.0 + (_pulseController.value * 0.05)
                            : 1.0,
                    child: ElevatedButton(
                      onPressed:
                          _selectedAnswerIndex != null ? _submitAnswer : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: _selectedAnswerIndex != null ? 4 : 0,
                      ),
                      child: const Text(
                        'إرسال الإجابة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

          // التفسير
          if (_showResult)
            AnimatedWidgets.fadeInCard(
              index: question.options.length + 2,
              child: Container(
                margin: const EdgeInsets.only(top: 20),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.primaryWithOpacity(
                    theme.colorScheme.primary,
                    0.05,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppColors.primaryWithOpacity(
                      theme.colorScheme.primary,
                      0.2,
                    ),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb, color: theme.colorScheme.primary),
                        const SizedBox(width: 8),
                        Text(
                          'التفسير',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      question.explanation,
                      style: const TextStyle(fontSize: 16, height: 1.5),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'المصدر: ${question.source}',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.primaryWithOpacity(
                          theme.colorScheme.onSurface,
                          0.6,
                        ),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAlreadyAnsweredView(DailyQuestionService questionService) {
    return Center(
      child: AnimatedWidgets.fadeInCard(
        index: 0,
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(30),
                decoration: BoxDecoration(
                  color: AppColors.greenWithOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 80,
                ),
              ),
              const SizedBox(height: 30),
              const Text(
                'تم الإجابة على سؤال اليوم! 🎉',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'عد غداً لسؤال جديد',
                style: TextStyle(fontSize: 16, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 30),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    PageTransitions.slideFromRight(const QuestionStatsScreen()),
                  );
                },
                icon: const Icon(Icons.analytics),
                label: const Text('عرض الإحصائيات'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNoQuestionView() {
    return const Center(
      child: Text('لا يوجد سؤال متاح حالياً', style: TextStyle(fontSize: 18)),
    );
  }

  Color _getDifficultyColor(int difficulty) {
    switch (difficulty) {
      case 1:
        return Colors.green;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getOptionColor(
    bool isSelected,
    bool isCorrect,
    bool isWrong,
    ThemeData theme,
  ) {
    if (_showResult) {
      if (isCorrect) return AppColors.greenWithOpacity(0.1);
      if (isWrong) return AppColors.redWithOpacity(0.1);
    }
    if (isSelected) {
      return AppColors.primaryWithOpacity(theme.colorScheme.primary, 0.1);
    }
    return theme.cardColor;
  }

  Color _getOptionBorderColor(
    bool isSelected,
    bool isCorrect,
    bool isWrong,
    ThemeData theme,
  ) {
    if (_showResult) {
      if (isCorrect) return Colors.green;
      if (isWrong) return Colors.red;
    }
    if (isSelected) return theme.colorScheme.primary;
    return theme.brightness == Brightness.dark
        ? AppColors.greyWithOpacity(0.3)
        : AppColors.greyWithOpacity(0.2);
  }

  Color _getOptionIconColor(
    bool isSelected,
    bool isCorrect,
    bool isWrong,
    ThemeData theme,
  ) {
    if (_showResult) {
      if (isCorrect) return Colors.green;
      if (isWrong) return Colors.red;
    }
    if (isSelected) return theme.colorScheme.primary;
    return Colors.grey;
  }
}

// شاشة الإحصائيات (مؤقتة)
class QuestionStatsScreen extends StatelessWidget {
  const QuestionStatsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إحصائيات الأسئلة')),
      body: const Center(child: Text('شاشة الإحصائيات قيد التطوير')),
    );
  }
}
